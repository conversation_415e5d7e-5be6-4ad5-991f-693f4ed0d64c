<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端调试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .debug-section {
            background: #f5f5f5;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border-left: 4px solid #1890ff;
        }
        .status {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 4px;
            font-weight: bold;
            margin: 4px;
        }
        .success { background: #52c41a; color: white; }
        .error { background: #ff4d4f; color: white; }
        .warning { background: #faad14; color: white; }
        .info { background: #1890ff; color: white; }
        button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 4px;
        }
        button:hover { background: #40a9ff; }
        .code {
            background: #f0f0f0;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            margin: 10px 0;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>🔧 前端调试页面</h1>
    <p>这个页面可以帮助你诊断前端应用的问题。</p>

    <div class="debug-section">
        <h2>📋 基础检查</h2>
        <div id="basic-checks">
            <p>正在检查...</p>
        </div>
    </div>

    <div class="debug-section">
        <h2>🔑 认证状态</h2>
        <div id="auth-status">
            <p>Token 状态: <span id="token-status">检查中...</span></p>
            <p>当前页面: <span id="current-page">-</span></p>
        </div>
        <button onclick="clearAuth()">清除认证</button>
        <button onclick="setTestAuth()">设置测试认证</button>
        <button onclick="checkAuth()">刷新检查</button>
    </div>

    <div class="debug-section">
        <h2>🌐 网络检查</h2>
        <div id="network-status">
            <p>前端服务: <span id="frontend-status">检查中...</span></p>
            <p>API 服务: <span id="api-status">检查中...</span></p>
        </div>
        <button onclick="testNetwork()">测试网络连接</button>
    </div>

    <div class="debug-section">
        <h2>🧪 快速测试</h2>
        <button onclick="testLogin()">测试登录流程</button>
        <button onclick="testRouting()">测试路由跳转</button>
        <button onclick="testComponents()">测试组件加载</button>
        <div id="test-results"></div>
    </div>

    <div class="debug-section">
        <h2>📊 系统信息</h2>
        <div class="code" id="system-info">
            <p>正在收集系统信息...</p>
        </div>
    </div>

    <div class="debug-section">
        <h2>🔗 快速链接</h2>
        <button onclick="goToApp()">打开主应用</button>
        <button onclick="goToLogin()">打开登录页</button>
        <button onclick="goToDashboard()">打开仪表板</button>
    </div>

    <script>
        // 基础检查
        function runBasicChecks() {
            const checks = [];
            
            // 检查 localStorage
            try {
                localStorage.setItem('test', 'test');
                localStorage.removeItem('test');
                checks.push('<span class="status success">✓</span> localStorage 可用');
            } catch (e) {
                checks.push('<span class="status error">✗</span> localStorage 不可用');
            }
            
            // 检查 sessionStorage
            try {
                sessionStorage.setItem('test', 'test');
                sessionStorage.removeItem('test');
                checks.push('<span class="status success">✓</span> sessionStorage 可用');
            } catch (e) {
                checks.push('<span class="status error">✗</span> sessionStorage 不可用');
            }
            
            // 检查 fetch API
            if (typeof fetch !== 'undefined') {
                checks.push('<span class="status success">✓</span> Fetch API 可用');
            } else {
                checks.push('<span class="status error">✗</span> Fetch API 不可用');
            }
            
            // 检查 ES6 支持
            try {
                eval('const test = () => {}');
                checks.push('<span class="status success">✓</span> ES6 支持');
            } catch (e) {
                checks.push('<span class="status error">✗</span> ES6 不支持');
            }
            
            document.getElementById('basic-checks').innerHTML = checks.join('<br>');
        }

        // 检查认证状态
        function checkAuth() {
            const token = localStorage.getItem('authToken');
            const tokenStatus = document.getElementById('token-status');
            const currentPage = document.getElementById('current-page');
            
            if (token) {
                tokenStatus.innerHTML = '<span class="status success">已登录</span> ' + token.substring(0, 20) + '...';
            } else {
                tokenStatus.innerHTML = '<span class="status error">未登录</span>';
            }
            
            currentPage.textContent = window.location.pathname;
        }

        // 清除认证
        function clearAuth() {
            localStorage.removeItem('authToken');
            localStorage.removeItem('rememberMe');
            checkAuth();
            alert('认证信息已清除');
        }

        // 设置测试认证
        function setTestAuth() {
            const testToken = 'debug_token_' + Date.now();
            localStorage.setItem('authToken', testToken);
            checkAuth();
            alert('测试认证已设置');
        }

        // 测试网络连接
        async function testNetwork() {
            const frontendStatus = document.getElementById('frontend-status');
            const apiStatus = document.getElementById('api-status');
            
            // 测试前端服务
            try {
                const response = await fetch(window.location.origin);
                if (response.ok) {
                    frontendStatus.innerHTML = '<span class="status success">正常</span>';
                } else {
                    frontendStatus.innerHTML = '<span class="status warning">异常</span>';
                }
            } catch (e) {
                frontendStatus.innerHTML = '<span class="status error">无法连接</span>';
            }
            
            // 测试 API 服务
            try {
                const response = await fetch('http://localhost:8080/health', { 
                    method: 'GET',
                    mode: 'cors'
                });
                if (response.ok) {
                    apiStatus.innerHTML = '<span class="status success">正常</span>';
                } else {
                    apiStatus.innerHTML = '<span class="status warning">异常</span>';
                }
            } catch (e) {
                apiStatus.innerHTML = '<span class="status error">无法连接</span> (这是正常的，如果没有启动后端服务)';
            }
        }

        // 测试登录流程
        function testLogin() {
            const results = document.getElementById('test-results');
            results.innerHTML = '<span class="status info">测试登录流程...</span>';
            
            // 清除现有认证
            localStorage.removeItem('authToken');
            
            // 模拟登录
            setTimeout(() => {
                const token = 'test_login_' + Date.now();
                localStorage.setItem('authToken', token);
                results.innerHTML = '<span class="status success">✓ 登录流程测试完成</span>';
                checkAuth();
            }, 1000);
        }

        // 测试路由跳转
        function testRouting() {
            const results = document.getElementById('test-results');
            results.innerHTML = '<span class="status info">测试路由跳转...</span>';
            
            // 确保有认证
            if (!localStorage.getItem('authToken')) {
                localStorage.setItem('authToken', 'test_routing_' + Date.now());
            }
            
            // 测试跳转到仪表板
            setTimeout(() => {
                window.location.href = '/dashboard';
            }, 1000);
        }

        // 测试组件加载
        function testComponents() {
            const results = document.getElementById('test-results');
            results.innerHTML = '<span class="status info">测试组件加载...</span>';
            
            // 这里可以添加更多的组件测试逻辑
            setTimeout(() => {
                results.innerHTML = '<span class="status success">✓ 组件加载测试完成</span>';
            }, 1000);
        }

        // 收集系统信息
        function collectSystemInfo() {
            const info = {
                'User Agent': navigator.userAgent,
                'Platform': navigator.platform,
                'Language': navigator.language,
                'Cookie Enabled': navigator.cookieEnabled,
                'Online': navigator.onLine,
                'Screen Resolution': `${screen.width}x${screen.height}`,
                'Viewport Size': `${window.innerWidth}x${window.innerHeight}`,
                'Current URL': window.location.href,
                'Referrer': document.referrer || 'None',
                'Local Storage': typeof(Storage) !== "undefined" ? 'Supported' : 'Not Supported',
                'Session Storage': typeof(Storage) !== "undefined" ? 'Supported' : 'Not Supported'
            };
            
            let infoHtml = '';
            for (const [key, value] of Object.entries(info)) {
                infoHtml += `<strong>${key}:</strong> ${value}<br>`;
            }
            
            document.getElementById('system-info').innerHTML = infoHtml;
        }

        // 快速链接
        function goToApp() {
            window.location.href = '/';
        }

        function goToLogin() {
            window.location.href = '/login';
        }

        function goToDashboard() {
            // 确保有认证
            if (!localStorage.getItem('authToken')) {
                localStorage.setItem('authToken', 'debug_token_' + Date.now());
            }
            window.location.href = '/dashboard';
        }

        // 页面加载时运行检查
        window.onload = function() {
            runBasicChecks();
            checkAuth();
            testNetwork();
            collectSystemInfo();
        };
    </script>
</body>
</html>
