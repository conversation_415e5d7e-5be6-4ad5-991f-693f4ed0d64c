{"name": "@vue/devtools-api", "version": "6.6.4", "description": "Interact with the Vue devtools from the page", "author": {"name": "<PERSON>"}, "license": "MIT", "repository": {"url": "https://github.com/vuejs/vue-devtools.git", "type": "git", "directory": "packages/api"}, "sideEffects": false, "main": "lib/cjs/index.js", "browser": "lib/esm/index.js", "module": "lib/esm/index.js", "types": "lib/esm/index.d.ts", "files": ["lib/cjs", "lib/esm"], "publishConfig": {"access": "public"}, "scripts": {"build": "rimraf lib && yarn build:esm && yarn build:cjs", "build:esm": "tsc --module es2015 --outDir lib/esm -d", "build:cjs": "tsc --module commonjs --outDir lib/cjs", "build:watch": "yarn tsc --module es2015 --outDir lib/esm -d -w --sourceMap"}, "devDependencies": {"@types/node": "^20.11.16", "@types/webpack-env": "^1.15.1", "typescript": "^5.3.3"}}