import type { DataIndex, ColumnType, CustomizeComponent, DefaultRecordType, AlignType, CellEllipsisType, TransformCellText, AdditionalProps } from '../interface';
export interface CellProps<RecordType = DefaultRecordType> {
    prefixCls?: string;
    record?: RecordType;
    /** `column` index is the real show rowIndex */
    index?: number;
    /** the index of the record. For the render(value, record, renderIndex) */
    renderIndex?: number;
    dataIndex?: DataIndex;
    customRender?: ColumnType<RecordType>['customRender'];
    component?: CustomizeComponent;
    colSpan?: number;
    rowSpan?: number;
    ellipsis?: CellEllipsisType;
    align?: AlignType;
    fixLeft?: number | false;
    fixRight?: number | false;
    firstFixLeft?: boolean;
    lastFixLeft?: boolean;
    firstFixRight?: boolean;
    lastFixRight?: boolean;
    /** @private Used for `expandable` with nest tree */
    appendNode?: any;
    additionalProps?: AdditionalProps;
    rowType?: 'header' | 'body' | 'footer';
    isSticky?: boolean;
    column?: ColumnType<RecordType>;
    cellType?: 'header' | 'body';
    transformCellText?: TransformCellText<RecordType>;
}
declare const _default: import("vue").DefineComponent<{
    index?: any;
    ellipsis?: any;
    column?: any;
    prefixCls?: any;
    align?: any;
    component?: any;
    colSpan?: any;
    dataIndex?: any;
    customRender?: any;
    rowSpan?: any;
    record?: any;
    renderIndex?: any;
    fixLeft?: any;
    fixRight?: any;
    firstFixLeft?: any;
    lastFixLeft?: any;
    firstFixRight?: any;
    lastFixRight?: any;
    appendNode?: any;
    additionalProps?: any;
    rowType?: any;
    isSticky?: any;
    cellType?: any;
    transformCellText?: any;
}, () => import("vue/jsx-runtime").JSX.Element, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<{
    index?: any;
    ellipsis?: any;
    column?: any;
    prefixCls?: any;
    align?: any;
    component?: any;
    colSpan?: any;
    dataIndex?: any;
    customRender?: any;
    rowSpan?: any;
    record?: any;
    renderIndex?: any;
    fixLeft?: any;
    fixRight?: any;
    firstFixLeft?: any;
    lastFixLeft?: any;
    firstFixRight?: any;
    lastFixRight?: any;
    appendNode?: any;
    additionalProps?: any;
    rowType?: any;
    isSticky?: any;
    cellType?: any;
    transformCellText?: any;
}> & Readonly<{}>, {}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;
