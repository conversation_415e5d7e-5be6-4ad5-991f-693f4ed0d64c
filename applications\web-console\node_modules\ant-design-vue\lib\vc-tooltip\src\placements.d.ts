export declare const placements: {
    left: {
        points: string[];
        overflow: {
            adjustX: number;
            adjustY: number;
        };
        offset: number[];
        targetOffset: number[];
    };
    right: {
        points: string[];
        overflow: {
            adjustX: number;
            adjustY: number;
        };
        offset: number[];
        targetOffset: number[];
    };
    top: {
        points: string[];
        overflow: {
            adjustX: number;
            adjustY: number;
        };
        offset: number[];
        targetOffset: number[];
    };
    bottom: {
        points: string[];
        overflow: {
            adjustX: number;
            adjustY: number;
        };
        offset: number[];
        targetOffset: number[];
    };
    topLeft: {
        points: string[];
        overflow: {
            adjustX: number;
            adjustY: number;
        };
        offset: number[];
        targetOffset: number[];
    };
    leftTop: {
        points: string[];
        overflow: {
            adjustX: number;
            adjustY: number;
        };
        offset: number[];
        targetOffset: number[];
    };
    topRight: {
        points: string[];
        overflow: {
            adjustX: number;
            adjustY: number;
        };
        offset: number[];
        targetOffset: number[];
    };
    rightTop: {
        points: string[];
        overflow: {
            adjustX: number;
            adjustY: number;
        };
        offset: number[];
        targetOffset: number[];
    };
    bottomRight: {
        points: string[];
        overflow: {
            adjustX: number;
            adjustY: number;
        };
        offset: number[];
        targetOffset: number[];
    };
    rightBottom: {
        points: string[];
        overflow: {
            adjustX: number;
            adjustY: number;
        };
        offset: number[];
        targetOffset: number[];
    };
    bottomLeft: {
        points: string[];
        overflow: {
            adjustX: number;
            adjustY: number;
        };
        offset: number[];
        targetOffset: number[];
    };
    leftBottom: {
        points: string[];
        overflow: {
            adjustX: number;
            adjustY: number;
        };
        offset: number[];
        targetOffset: number[];
    };
};
export default placements;
