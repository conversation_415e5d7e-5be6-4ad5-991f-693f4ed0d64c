{"name": "web-console", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview", "test-start": "node test-start.js", "check": "vue-tsc --noEmit"}, "dependencies": {"ant-design-vue": "^4.2.6", "axios": "^1.10.0", "vue": "^3.5.17", "vue-router": "^4.4.5"}, "devDependencies": {"@vitejs/plugin-vue": "^6.0.0", "@vue/tsconfig": "^0.7.0", "typescript": "~5.8.3", "vite": "^7.0.4", "vue-tsc": "^2.2.12"}}