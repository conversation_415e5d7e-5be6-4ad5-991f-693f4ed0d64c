<template>
  <router-view />
</template>

<script setup lang="ts">
// App.vue 现在只需要渲染路由视图
// 所有的布局和导航逻辑都在各自的组件中处理
</script>

<style>
/* 全局样式 */
#app {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 重置一些默认样式 */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
}

/* Ant Design 样式覆盖 */
.ant-layout {
  min-height: 100vh;
}

.ant-layout-sider {
  box-shadow: 2px 0 6px rgba(0, 21, 41, 0.35);
}

.ant-menu-dark {
  background: #001529;
}

.ant-menu-dark .ant-menu-item-selected {
  background-color: #1890ff;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
