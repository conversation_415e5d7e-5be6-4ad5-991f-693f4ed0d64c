<template>
  <a-layout style="min-height: 100vh">
    <!-- 左侧可折叠侧边栏 -->
    <a-layout-sider 
      v-model:collapsed="collapsed" 
      :trigger="null" 
      collapsible
      :width="220"
      :collapsed-width="80"
      theme="dark"
    >
      <!-- Logo 区域 -->
      <div class="logo">
        <div v-if="!collapsed" class="logo-expanded">
          <img src="/vite.svg" alt="Logo" class="logo-icon" />
          <span class="logo-text">雅典娜平台</span>
        </div>
        <div v-else class="logo-collapsed">
          <img src="/vite.svg" alt="Logo" class="logo-icon" />
        </div>
      </div>
      
      <!-- 导航菜单 -->
      <a-menu
        theme="dark"
        mode="inline"
        :selected-keys="selectedKeys"
        :open-keys="openKeys"
        @click="handleMenuClick"
        @openChange="handleOpenChange"
      >
        <a-menu-item key="dashboard">
          <template #icon>
            <DashboardOutlined />
          </template>
          <span>仪表板</span>
        </a-menu-item>
        
        <a-menu-item key="tasks">
          <template #icon>
            <UnorderedListOutlined />
          </template>
          <span>任务管理</span>
        </a-menu-item>
        
        <a-menu-item key="simulation">
          <template #icon>
            <ExperimentOutlined />
          </template>
          <span>模拟执行</span>
        </a-menu-item>
        
        <a-menu-item key="reports">
          <template #icon>
            <FileTextOutlined />
          </template>
          <span>分析报告</span>
        </a-menu-item>
        
        <!-- 子菜单示例 -->
        <a-sub-menu key="system">
          <template #icon>
            <SettingOutlined />
          </template>
          <template #title>系统管理</template>
          <a-menu-item key="users">
            <template #icon>
              <UserOutlined />
            </template>
            <span>用户管理</span>
          </a-menu-item>
          <a-menu-item key="settings">
            <template #icon>
              <ToolOutlined />
            </template>
            <span>系统设置</span>
          </a-menu-item>
        </a-sub-menu>
      </a-menu>
    </a-layout-sider>
    
    <!-- 右侧主内容区 -->
    <a-layout>
      <!-- 顶部导航栏 -->
      <a-layout-header class="layout-header">
        <div class="header-left">
          <!-- 折叠按钮 -->
          <a-button
            type="text"
            class="trigger"
            @click="toggleCollapsed"
          >
            <MenuUnfoldOutlined v-if="collapsed" />
            <MenuFoldOutlined v-else />
          </a-button>
          
          <!-- 面包屑导航 -->
          <a-breadcrumb class="breadcrumb">
            <a-breadcrumb-item>
              <HomeOutlined />
              <span>首页</span>
            </a-breadcrumb-item>
            <a-breadcrumb-item>{{ currentPageTitle }}</a-breadcrumb-item>
          </a-breadcrumb>
        </div>
        
        <div class="header-right">
          <!-- 通知图标 -->
          <a-badge :count="notificationCount" class="header-action">
            <a-button type="text" shape="circle">
              <BellOutlined />
            </a-button>
          </a-badge>
          
          <!-- 全屏按钮 -->
          <a-button type="text" shape="circle" class="header-action" @click="toggleFullscreen">
            <FullscreenOutlined v-if="!isFullscreen" />
            <FullscreenExitOutlined v-else />
          </a-button>
          
          <!-- 用户下拉菜单 -->
          <a-dropdown placement="bottomRight">
            <div class="user-info">
              <a-avatar size="small" class="user-avatar">
                <template #icon>
                  <UserOutlined />
                </template>
              </a-avatar>
              <span class="user-name">管理员</span>
              <DownOutlined class="user-dropdown-icon" />
            </div>
            
            <template #overlay>
              <a-menu @click="handleUserMenuClick">
                <a-menu-item key="profile">
                  <UserOutlined />
                  <span>个人中心</span>
                </a-menu-item>
                <a-menu-item key="settings">
                  <SettingOutlined />
                  <span>个人设置</span>
                </a-menu-item>
                <a-menu-divider />
                <a-menu-item key="logout">
                  <LogoutOutlined />
                  <span>退出登录</span>
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </div>
      </a-layout-header>
      
      <!-- 主内容区域 -->
      <a-layout-content class="layout-content">
        <div class="content-wrapper">
          <router-view />
        </div>
      </a-layout-content>
      
      <!-- 底部 -->
      <a-layout-footer class="layout-footer">
        <div class="footer-content">
          <span>雅典娜水资源管理平台 ©2025 Created by Athena Team</span>
          <div class="footer-links">
            <a href="#" @click.prevent>帮助文档</a>
            <a-divider type="vertical" />
            <a href="#" @click.prevent>隐私政策</a>
            <a-divider type="vertical" />
            <a href="#" @click.prevent>服务条款</a>
          </div>
        </div>
      </a-layout-footer>
    </a-layout>
  </a-layout>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { message } from 'ant-design-vue'
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  DashboardOutlined,
  UnorderedListOutlined,
  ExperimentOutlined,
  FileTextOutlined,
  SettingOutlined,
  UserOutlined,
  ToolOutlined,
  HomeOutlined,
  BellOutlined,
  FullscreenOutlined,
  FullscreenExitOutlined,
  DownOutlined,
  LogoutOutlined
} from '@ant-design/icons-vue'

const router = useRouter()
const route = useRoute()

// 响应式数据
const collapsed = ref(false)
const openKeys = ref(['system'])
const notificationCount = ref(3)
const isFullscreen = ref(false)

// 当前选中的菜单项
const selectedKeys = computed(() => {
  const path = route.path
  if (path.includes('dashboard')) return ['dashboard']
  if (path.includes('tasks')) return ['tasks']
  if (path.includes('simulation')) return ['simulation']
  if (path.includes('reports')) return ['reports']
  if (path.includes('users')) return ['users']
  if (path.includes('settings')) return ['settings']
  return ['dashboard']
})

// 当前页面标题
const currentPageTitle = computed(() => {
  const titleMap: Record<string, string> = {
    dashboard: '仪表板',
    tasks: '任务管理',
    simulation: '模拟执行',
    reports: '分析报告',
    users: '用户管理',
    settings: '系统设置'
  }
  
  const key = selectedKeys.value[0]
  return titleMap[key] || '仪表板'
})

// 方法
const toggleCollapsed = () => {
  collapsed.value = !collapsed.value
}

const handleMenuClick = ({ key }: { key: string }) => {
  // 处理子菜单项
  if (key === 'users' || key === 'settings') {
    message.info(`${key === 'users' ? '用户管理' : '系统设置'}功能开发中...`)
    return
  }
  
  // 导航到对应路由
  router.push(`/${key}`)
}

const handleOpenChange = (keys: string[]) => {
  openKeys.value = keys
}

const handleUserMenuClick = ({ key }: { key: string }) => {
  switch (key) {
    case 'profile':
      message.info('个人中心功能开发中...')
      break
    case 'settings':
      message.info('个人设置功能开发中...')
      break
    case 'logout':
      handleLogout()
      break
  }
}

const handleLogout = () => {
  // 清除认证信息
  localStorage.removeItem('authToken')
  
  // 显示退出成功消息
  message.success('退出登录成功')
  
  // 跳转到登录页
  router.push('/login')
}

const toggleFullscreen = () => {
  if (!document.fullscreenElement) {
    document.documentElement.requestFullscreen()
    isFullscreen.value = true
  } else {
    document.exitFullscreen()
    isFullscreen.value = false
  }
}

// 监听全屏状态变化
const handleFullscreenChange = () => {
  isFullscreen.value = !!document.fullscreenElement
}

// 生命周期
onMounted(() => {
  document.addEventListener('fullscreenchange', handleFullscreenChange)
})

onUnmounted(() => {
  document.removeEventListener('fullscreenchange', handleFullscreenChange)
})
</script>

<style scoped>
/* Logo 样式 */
.logo {
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  margin: 16px;
  border-radius: 8px;
  transition: all 0.3s;
}

.logo-expanded {
  display: flex;
  align-items: center;
  gap: 8px;
}

.logo-collapsed {
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo-icon {
  width: 32px;
  height: 32px;
}

.logo-text {
  color: white;
  font-size: 16px;
  font-weight: 600;
}

/* 头部样式 */
.layout-header {
  background: #fff;
  padding: 0 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  position: sticky;
  top: 0;
  z-index: 10;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.trigger {
  font-size: 18px;
  line-height: 64px;
  cursor: pointer;
  transition: color 0.3s;
}

.trigger:hover {
  color: #1890ff;
}

.breadcrumb {
  margin: 0;
}

.header-action {
  cursor: pointer;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.user-info:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

.user-avatar {
  background-color: #1890ff;
}

.user-name {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
}

.user-dropdown-icon {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
}

/* 内容区样式 */
.layout-content {
  margin: 24px 16px 0;
  overflow: initial;
}

.content-wrapper {
  padding: 24px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  min-height: 360px;
}

/* 底部样式 */
.layout-footer {
  text-align: center;
  background: #f0f2f5;
  padding: 12px 16px;
  margin-top: 24px;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
}

.footer-links {
  display: flex;
  align-items: center;
  gap: 8px;
}

.footer-links a {
  color: rgba(0, 0, 0, 0.65);
  text-decoration: none;
  transition: color 0.3s;
}

.footer-links a:hover {
  color: #1890ff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .layout-header {
    padding: 0 16px;
  }
  
  .header-left {
    gap: 8px;
  }
  
  .header-right {
    gap: 8px;
  }
  
  .user-name {
    display: none;
  }
  
  .footer-content {
    flex-direction: column;
    gap: 8px;
  }
  
  .layout-content {
    margin: 16px 8px 0;
  }
  
  .content-wrapper {
    padding: 16px;
  }
}

/* 菜单样式优化 */
:deep(.ant-menu-dark) {
  background: #001529;
}

:deep(.ant-menu-dark .ant-menu-item-selected) {
  background-color: #1890ff;
}

:deep(.ant-menu-dark .ant-menu-item:hover) {
  background-color: rgba(24, 144, 255, 0.2);
}

:deep(.ant-layout-sider) {
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);
}
</style>
