# Layouts 目录说明

## 目录结构
```
src/layouts/
├── MainLayout.vue     # 主布局组件
└── README.md         # 说明文档
```

## MainLayout.vue 功能特性

### 🎨 **布局结构**
- **左侧侧边栏**: 可折叠的导航菜单
- **右侧主区域**: 包含头部、内容区和底部
- **响应式设计**: 适配移动端和桌面端

### 🧭 **导航菜单**
- **仪表板** (`/dashboard`) - 系统概览
- **任务管理** (`/tasks`) - 任务列表和管理
- **模拟执行** (`/simulation`) - 创建新的模拟任务
- **分析报告** (`/reports`) - 查看分析报告
- **系统管理** (子菜单)
  - 用户管理 (开发中)
  - 系统设置 (开发中)

### 🔧 **头部功能**
- **折叠按钮**: 控制侧边栏展开/收起
- **面包屑导航**: 显示当前页面路径
- **通知图标**: 显示通知数量 (Badge)
- **全屏按钮**: 切换全屏模式
- **用户菜单**: 个人中心、设置、退出登录

### 📱 **响应式特性**
- 移动端自动隐藏用户名
- 调整间距和布局
- 优化触摸操作

### 🎯 **核心功能**

#### 1. 侧边栏折叠
```vue
<a-layout-sider 
  v-model:collapsed="collapsed" 
  :trigger="null" 
  collapsible
  :width="220"
  :collapsed-width="80"
>
```

#### 2. 菜单路由导航
```vue
<a-menu
  theme="dark"
  mode="inline"
  :selected-keys="selectedKeys"
  @click="handleMenuClick"
>
```

#### 3. 用户认证
```javascript
const handleLogout = () => {
  localStorage.removeItem('authToken')
  message.success('退出登录成功')
  router.push('/login')
}
```

#### 4. 全屏功能
```javascript
const toggleFullscreen = () => {
  if (!document.fullscreenElement) {
    document.documentElement.requestFullscreen()
  } else {
    document.exitFullscreen()
  }
}
```

### 🎨 **样式特点**
- **深色侧边栏**: 使用 Ant Design 的 dark 主题
- **阴影效果**: 侧边栏和头部有适当的阴影
- **圆角设计**: Logo 区域和内容区使用圆角
- **过渡动画**: 折叠、悬停等状态有平滑过渡

### 🔄 **状态管理**
- `collapsed`: 侧边栏折叠状态
- `selectedKeys`: 当前选中的菜单项
- `openKeys`: 展开的子菜单
- `notificationCount`: 通知数量
- `isFullscreen`: 全屏状态

### 📋 **计算属性**
- `selectedKeys`: 根据当前路由自动计算选中的菜单项
- `currentPageTitle`: 根据当前路由显示页面标题

### 🔗 **与路由的集成**
- 自动根据 `route.path` 高亮对应菜单项
- 点击菜单项自动导航到对应路由
- 面包屑导航显示当前页面位置

### 🛠️ **扩展指南**

#### 添加新的菜单项
1. 在 `<a-menu>` 中添加新的 `<a-menu-item>`
2. 在 `selectedKeys` 计算属性中添加路径判断
3. 在 `currentPageTitle` 中添加标题映射
4. 在 `handleMenuClick` 中添加导航逻辑

#### 添加子菜单
```vue
<a-sub-menu key="new-submenu">
  <template #icon>
    <IconComponent />
  </template>
  <template #title>子菜单标题</template>
  <a-menu-item key="submenu-item">
    <span>子菜单项</span>
  </a-menu-item>
</a-sub-menu>
```

#### 自定义样式
使用 `:deep()` 选择器覆盖 Ant Design 的默认样式：
```css
:deep(.ant-menu-dark .ant-menu-item-selected) {
  background-color: #1890ff;
}
```

### 📦 **依赖组件**
- Ant Design Vue Layout 组件
- Vue Router
- Ant Design Icons

### 🔍 **调试技巧**
- 使用 Vue DevTools 查看组件状态
- 检查 `route.path` 确认路由匹配
- 查看控制台的路由守卫日志
