import { createRouter, createWebHistory, RouteRecordRaw } from 'vue-router'
import LoginView from '../views/LoginView.vue'
import MainLayout from '../views/MainLayout.vue'
import DashboardView from '../views/DashboardView.vue'
import TasksView from '../views/TasksView.vue'
import SimulationView from '../views/SimulationView.vue'
import ReportsView from '../views/ReportsView.vue'

const routes: Array<RouteRecordRaw> = [
  {
    path: '/login',
    name: 'Login',
    component: LoginView,
    meta: { requiresAuth: false }
  },
  {
    path: '/',
    component: MainLayout,
    meta: { requiresAuth: true },
    children: [
      {
        path: '',
        redirect: '/dashboard'
      },
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: DashboardView,
        meta: { 
          requiresAuth: true,
          title: '仪表板'
        }
      },
      {
        path: 'tasks',
        name: 'Tasks',
        component: TasksView,
        meta: { 
          requiresAuth: true,
          title: '任务管理'
        }
      },
      {
        path: 'simulation',
        name: 'Simulation',
        component: SimulationView,
        meta: { 
          requiresAuth: true,
          title: '模拟执行'
        }
      },
      {
        path: 'reports',
        name: 'Reports',
        component: ReportsView,
        meta: { 
          requiresAuth: true,
          title: '分析报告'
        }
      }
    ]
  },
  {
    // 404 页面
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    redirect: '/dashboard'
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 全局前置守卫
router.beforeEach((to, from, next) => {
  // 检查路由是否需要认证
  const requiresAuth = to.matched.some(record => record.meta.requiresAuth)
  
  // 从 localStorage 获取 token
  const token = localStorage.getItem('authToken')
  
  console.log('路由守卫检查:', {
    to: to.path,
    requiresAuth,
    hasToken: !!token
  })
  
  if (requiresAuth && !token) {
    // 需要认证但没有 token，重定向到登录页
    console.log('未登录，重定向到登录页')
    next('/login')
  } else if (to.path === '/login' && token) {
    // 已登录用户访问登录页，重定向到首页
    console.log('已登录，重定向到首页')
    next('/dashboard')
  } else {
    // 允许访问
    next()
  }
})

// 全局后置钩子 - 设置页面标题
router.afterEach((to) => {
  const title = to.meta.title as string
  if (title) {
    document.title = `${title} - 雅典娜水资源管理平台`
  } else {
    document.title = '雅典娜水资源管理平台'
  }
})

export default router
