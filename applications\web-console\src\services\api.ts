import axios from 'axios'

const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL
})

api.interceptors.request.use(config => {
  const token = localStorage.getItem('authToken')  // 使用 authToken 保持一致
  if (token) {
    config.headers = config.headers || {}
    config.headers['Authorization'] = `Bearer ${token}`
  }
  return config
})

export default {
  login(userData: { username: string; password: string }) {
    return api.post('/auth/login', userData)
  },
  getTasks() {
    return api.get('/api/tasks')
  }
} 