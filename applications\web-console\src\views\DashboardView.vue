<template>
  <div>
    <a-page-header
      title="仪表板"
      sub-title="系统概览和关键指标"
    />
    
    <!-- 统计卡片 -->
    <a-row :gutter="16" style="margin-bottom: 24px;">
      <a-col :span="6">
        <a-card>
          <a-statistic
            title="总任务数"
            :value="statistics.totalTasks"
            :value-style="{ color: '#3f8600' }"
          >
            <template #prefix>
              <UnorderedListOutlined />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
      
      <a-col :span="6">
        <a-card>
          <a-statistic
            title="运行中任务"
            :value="statistics.runningTasks"
            :value-style="{ color: '#1890ff' }"
          >
            <template #prefix>
              <SyncOutlined spin />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
      
      <a-col :span="6">
        <a-card>
          <a-statistic
            title="已完成任务"
            :value="statistics.completedTasks"
            :value-style="{ color: '#52c41a' }"
          >
            <template #prefix>
              <CheckCircleOutlined />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
      
      <a-col :span="6">
        <a-card>
          <a-statistic
            title="系统状态"
            value="正常"
            :value-style="{ color: '#52c41a' }"
          >
            <template #prefix>
              <HeartOutlined />
            </template>
          </a-statistic>
        </a-card>
      </a-col>
    </a-row>
    
    <!-- 内容区域 -->
    <a-row :gutter="16">
      <!-- 最近任务 -->
      <a-col :span="12">
        <a-card title="最近任务" :bordered="false">
          <a-list
            :data-source="recentTasks"
            :loading="loading"
          >
            <template #renderItem="{ item }">
              <a-list-item>
                <a-list-item-meta
                  :title="item.name"
                  :description="`创建时间: ${item.createTime}`"
                >
                  <template #avatar>
                    <a-avatar :style="{ backgroundColor: getStatusColor(item.status) }">
                      {{ item.status }}
                    </a-avatar>
                  </template>
                </a-list-item-meta>
              </a-list-item>
            </template>
          </a-list>
        </a-card>
      </a-col>
      
      <!-- 系统信息 -->
      <a-col :span="12">
        <a-card title="系统信息" :bordered="false">
          <a-descriptions :column="1">
            <a-descriptions-item label="平台版本">v0.14.0</a-descriptions-item>
            <a-descriptions-item label="运行时间">{{ uptime }}</a-descriptions-item>
            <a-descriptions-item label="服务状态">
              <a-tag color="green">正常运行</a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="最后更新">{{ lastUpdate }}</a-descriptions-item>
          </a-descriptions>
          
          <a-divider />
          
          <a-space>
            <a-button type="primary" @click="refreshData">
              <ReloadOutlined />
              刷新数据
            </a-button>
            <a-button @click="$router.push('/simulation')">
              <ExperimentOutlined />
              创建模拟
            </a-button>
          </a-space>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  UnorderedListOutlined,
  SyncOutlined,
  CheckCircleOutlined,
  HeartOutlined,
  ReloadOutlined,
  ExperimentOutlined
} from '@ant-design/icons-vue'

// 响应式数据
const loading = ref(false)
const statistics = ref({
  totalTasks: 0,
  runningTasks: 0,
  completedTasks: 0
})

const recentTasks = ref([
  {
    id: 1,
    name: '北京水资源模拟',
    status: '完成',
    createTime: '2025-07-17 14:30:00'
  },
  {
    id: 2,
    name: 'Rivertown水资源模拟',
    status: '运行',
    createTime: '2025-07-17 15:45:00'
  },
  {
    id: 3,
    name: '上海水资源模拟',
    status: '等待',
    createTime: '2025-07-17 16:20:00'
  }
])

const uptime = ref('2天 14小时 32分钟')
const lastUpdate = ref(new Date().toLocaleString())

// 获取状态颜色
const getStatusColor = (status: string) => {
  switch (status) {
    case '完成':
      return '#52c41a'
    case '运行':
      return '#1890ff'
    case '等待':
      return '#faad14'
    case '失败':
      return '#f5222d'
    default:
      return '#d9d9d9'
  }
}

// 刷新数据
const refreshData = async () => {
  loading.value = true
  try {
    // 模拟 API 调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 更新统计数据
    statistics.value = {
      totalTasks: Math.floor(Math.random() * 100) + 50,
      runningTasks: Math.floor(Math.random() * 10) + 1,
      completedTasks: Math.floor(Math.random() * 80) + 40
    }
    
    lastUpdate.value = new Date().toLocaleString()
    message.success('数据刷新成功')
  } catch (error) {
    message.error('数据刷新失败')
  } finally {
    loading.value = false
  }
}

// 组件挂载时加载数据
onMounted(() => {
  refreshData()
})
</script>

<style scoped>
.ant-statistic-content {
  font-size: 24px;
}
</style>
