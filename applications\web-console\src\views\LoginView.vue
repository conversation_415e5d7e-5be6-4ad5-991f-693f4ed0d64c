<template>
  <div style="min-height: 100vh; display: flex; align-items: center; justify-content: center;">
    <a-card style="width: 350px;">
      <a-form @submit.prevent="handleLogin">
        <a-form-item>
          <a-input v-model:value="form.username" placeholder="用户名" />
        </a-form-item>
        <a-form-item>
          <a-input v-model:value="form.password" type="password" placeholder="密码" />
        </a-form-item>
        <a-form-item>
          <a-button type="primary" block @click="handleLogin">登录</a-button>
        </a-form-item>
      </a-form>
    </a-card>
  </div>
</template>

<script setup>
import { reactive } from 'vue'
import { useRouter } from 'vue-router'
import api from '../services/api'
import { message as $message } from 'ant-design-vue'

const router = useRouter()
const form = reactive({
  username: '',
  password: ''
})

async function handleLogin() {
  try {
    const res = await api.login(form)
    const token = res.data?.token
    if (token) {
      localStorage.setItem('token', token)
      $message.success('登录成功')
      router.push('/dashboard')
    } else {
      $message.error('登录失败，未获取到 Token')
    }
  } catch (e) {
    $message.error('登录失败，用户名或密码错误')
  }
}
</script> 