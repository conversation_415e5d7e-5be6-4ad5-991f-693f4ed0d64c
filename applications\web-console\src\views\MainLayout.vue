<template>
  <a-layout style="min-height: 100vh">
    <!-- 侧边栏 -->
    <a-layout-sider v-model:collapsed="collapsed" :trigger="null" collapsible>
      <div class="logo">
        <h3 v-if="!collapsed" style="color: white; text-align: center; margin: 16px 0;">
          雅典娜平台
        </h3>
        <h3 v-else style="color: white; text-align: center; margin: 16px 0;">
          雅典娜
        </h3>
      </div>
      
      <a-menu
        theme="dark"
        mode="inline"
        :selected-keys="selectedKeys"
        @click="handleMenuClick"
      >
        <a-menu-item key="dashboard">
          <DashboardOutlined />
          <span>仪表板</span>
        </a-menu-item>
        
        <a-menu-item key="tasks">
          <UnorderedListOutlined />
          <span>任务管理</span>
        </a-menu-item>
        
        <a-menu-item key="simulation">
          <ExperimentOutlined />
          <span>模拟执行</span>
        </a-menu-item>
        
        <a-menu-item key="reports">
          <FileTextOutlined />
          <span>分析报告</span>
        </a-menu-item>
      </a-menu>
    </a-layout-sider>
    
    <!-- 主内容区域 -->
    <a-layout>
      <!-- 顶部导航栏 -->
      <a-layout-header style="background: #fff; padding: 0; box-shadow: 0 1px 4px rgba(0,21,41,.08);">
        <div style="display: flex; justify-content: space-between; align-items: center; padding: 0 24px;">
          <!-- 左侧：折叠按钮和面包屑 -->
          <div style="display: flex; align-items: center;">
            <a-button
              type="text"
              :icon="collapsed ? h(MenuUnfoldOutlined) : h(MenuFoldOutlined)"
              @click="() => (collapsed = !collapsed)"
              style="font-size: 16px; width: 64px; height: 64px;"
            />
            
            <a-breadcrumb style="margin-left: 16px;">
              <a-breadcrumb-item>
                <HomeOutlined />
                <span>首页</span>
              </a-breadcrumb-item>
              <a-breadcrumb-item>{{ currentPageTitle }}</a-breadcrumb-item>
            </a-breadcrumb>
          </div>
          
          <!-- 右侧：用户信息和退出 -->
          <div style="display: flex; align-items: center;">
            <a-dropdown>
              <a-button type="text" style="height: 64px;">
                <UserOutlined />
                <span style="margin-left: 8px;">管理员</span>
                <DownOutlined style="margin-left: 8px;" />
              </a-button>
              
              <template #overlay>
                <a-menu @click="handleUserMenuClick">
                  <a-menu-item key="profile">
                    <UserOutlined />
                    个人信息
                  </a-menu-item>
                  <a-menu-item key="settings">
                    <SettingOutlined />
                    系统设置
                  </a-menu-item>
                  <a-menu-divider />
                  <a-menu-item key="logout">
                    <LogoutOutlined />
                    退出登录
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </div>
        </div>
      </a-layout-header>
      
      <!-- 内容区域 -->
      <a-layout-content style="margin: 24px 16px; padding: 24px; background: #fff; min-height: 280px;">
        <router-view />
      </a-layout-content>
      
      <!-- 底部 -->
      <a-layout-footer style="text-align: center; background: #f0f2f5;">
        雅典娜水资源管理平台 ©2025 Created by Athena Team
      </a-layout-footer>
    </a-layout>
  </a-layout>
</template>

<script setup lang="ts">
import { ref, computed, h } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { message } from 'ant-design-vue'
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  DashboardOutlined,
  UnorderedListOutlined,
  ExperimentOutlined,
  FileTextOutlined,
  HomeOutlined,
  UserOutlined,
  DownOutlined,
  SettingOutlined,
  LogoutOutlined
} from '@ant-design/icons-vue'

const router = useRouter()
const route = useRoute()

// 侧边栏折叠状态
const collapsed = ref(false)

// 当前选中的菜单项
const selectedKeys = computed(() => {
  const path = route.path
  if (path.includes('dashboard')) return ['dashboard']
  if (path.includes('tasks')) return ['tasks']
  if (path.includes('simulation')) return ['simulation']
  if (path.includes('reports')) return ['reports']
  return ['dashboard']
})

// 当前页面标题
const currentPageTitle = computed(() => {
  return route.meta.title as string || '仪表板'
})

// 处理菜单点击
const handleMenuClick = ({ key }: { key: string }) => {
  router.push(`/${key}`)
}

// 处理用户菜单点击
const handleUserMenuClick = ({ key }: { key: string }) => {
  switch (key) {
    case 'profile':
      message.info('个人信息功能开发中...')
      break
    case 'settings':
      message.info('系统设置功能开发中...')
      break
    case 'logout':
      handleLogout()
      break
  }
}

// 退出登录
const handleLogout = () => {
  // 清除 token
  localStorage.removeItem('authToken')
  
  // 显示退出成功消息
  message.success('退出登录成功')
  
  // 跳转到登录页
  router.push('/login')
}
</script>

<style scoped>
.logo {
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.2);
  margin: 16px;
  border-radius: 6px;
}

.ant-layout-header {
  position: sticky;
  top: 0;
  z-index: 1;
  width: 100%;
}

.ant-layout-content {
  overflow: initial;
}
</style>
