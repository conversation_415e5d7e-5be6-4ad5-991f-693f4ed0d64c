<template>
  <div>
    <a-page-header
      title="分析报告"
      sub-title="查看模拟任务的详细分析报告"
    />
    
    <!-- 任务选择 -->
    <a-card style="margin-bottom: 16px;">
      <a-row :gutter="16" align="middle">
        <a-col :span="8">
          <a-select
            v-model:value="selectedTaskId"
            placeholder="选择要查看的任务"
            style="width: 100%"
            @change="handleTaskChange"
            :loading="loadingTasks"
          >
            <a-select-option
              v-for="task in completedTasks"
              :key="task.id"
              :value="task.id"
            >
              {{ task.name }} ({{ task.cityName }})
            </a-select-option>
          </a-select>
        </a-col>
        
        <a-col :span="8">
          <a-input
            v-model:value="userQuestion"
            placeholder="输入您的问题（可选）"
            @press-enter="generateReport"
          />
        </a-col>
        
        <a-col :span="8">
          <a-space>
            <a-button 
              type="primary" 
              @click="generateReport"
              :loading="loadingReport"
              :disabled="!selectedTaskId"
            >
              <FileTextOutlined />
              生成报告
            </a-button>
            <a-button @click="exportReport" :disabled="!reportContent">
              <DownloadOutlined />
              导出
            </a-button>
          </a-space>
        </a-col>
      </a-row>
    </a-card>
    
    <!-- 报告内容 -->
    <a-card v-if="reportContent" title="分析报告" :bordered="false">
      <div class="report-content">
        <a-typography>
          <div v-html="formatReportContent(reportContent)"></div>
        </a-typography>
      </div>
    </a-card>
    
    <!-- 空状态 -->
    <a-card v-else-if="!loadingReport">
      <a-empty
        description="请选择一个已完成的任务来生成分析报告"
        :image="Empty.PRESENTED_IMAGE_SIMPLE"
      >
        <a-button type="primary" @click="$router.push('/simulation')">
          创建新模拟
        </a-button>
      </a-empty>
    </a-card>
    
    <!-- 加载状态 -->
    <a-card v-else>
      <div style="text-align: center; padding: 50px;">
        <a-spin size="large">
          <template #tip>
            <div style="margin-top: 16px;">
              正在生成分析报告，请稍候...
            </div>
          </template>
        </a-spin>
      </div>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { message, Empty } from 'ant-design-vue'
import {
  FileTextOutlined,
  DownloadOutlined
} from '@ant-design/icons-vue'

// 响应式数据
const loadingTasks = ref(false)
const loadingReport = ref(false)
const selectedTaskId = ref<number>()
const userQuestion = ref('')
const reportContent = ref('')

const completedTasks = ref([
  {
    id: 1,
    name: '北京水资源模拟',
    cityName: '北京',
    status: 'COMPLETED'
  },
  {
    id: 2,
    name: 'Rivertown水资源模拟',
    cityName: 'Rivertown',
    status: 'COMPLETED'
  },
  {
    id: 3,
    name: '上海水资源模拟',
    cityName: '上海',
    status: 'COMPLETED'
  }
])

// 处理任务选择变化
const handleTaskChange = (taskId: number) => {
  reportContent.value = ''
  userQuestion.value = ''
}

// 生成报告
const generateReport = async () => {
  if (!selectedTaskId.value) {
    message.warning('请先选择一个任务')
    return
  }
  
  loadingReport.value = true
  
  try {
    // 模拟 API 调用
    await new Promise(resolve => setTimeout(resolve, 3000))
    
    // 模拟报告内容
    const selectedTask = completedTasks.value.find(task => task.id === selectedTaskId.value)
    reportContent.value = generateMockReport(selectedTask, userQuestion.value)
    
    message.success('分析报告生成成功')
  } catch (error) {
    message.error('生成报告失败')
  } finally {
    loadingReport.value = false
  }
}

// 生成模拟报告内容
const generateMockReport = (task: any, question: string) => {
  const baseReport = `
# 雅典娜分析报告 | 任务ID: ${task.id} | ${task.name}

---

## 1. 核心发现 (Executive Summary)

- ✅ **模拟成功完成**：${task.name}的水资源模拟已成功完成，所有关键指标均已计算完毕。
- 📊 **最终水位**：经过模拟计算，最终水位为 **85.3 米**，相比初始水位下降了 14.7 米。
- ⚠️ **关键风险**：在当前参数条件下，水位下降幅度较大，建议关注水资源管理策略。

## 2. 模拟参数概览

| 参数 | 数值 | 单位 |
|------|------|------|
| 城市名称 | ${task.cityName} | - |
| 人口数量 | 2,000,000 | 人 |
| 降雨量 | 50 | mm |
| 温度 | 25 | °C |
| 初始水位 | 100.0 | m |
| 最终水位 | 85.3 | m |

## 3. 详细分析

### 3.1 水位变化分析
模拟期间水位呈现稳定下降趋势，主要原因包括：
- **用水需求增长**：人口基数较大，日常用水需求持续增长
- **降雨量偏低**：50mm的降雨量相对较少，补给不足
- **蒸发损失**：25°C的温度条件下，蒸发损失较为明显

### 3.2 政策影响评估
系统自动应用了以下水资源管理政策：
- **节水政策**：实施了节水因子0.8的限制措施
- **供水优化**：优化了供水分配策略
- **应急预案**：建立了水位预警机制

### 3.3 风险评估
- **短期风险**：水位下降速度在可控范围内
- **中期风险**：需要关注持续干旱的影响
- **长期风险**：建议制定更严格的节水措施

## 4. 建议措施

1. **加强节水宣传**：提高公众节水意识
2. **优化供水系统**：改善供水管网效率
3. **开发替代水源**：考虑雨水收集、再生水利用
4. **建立预警机制**：完善水位监测和预警系统

## 5. 结论

${task.name}的模拟结果显示，在当前参数条件下，水资源管理面临一定挑战。建议采取综合性措施，确保水资源的可持续利用。

---

*报告生成时间：${new Date().toLocaleString()}*
*分析引擎：雅典娜 AI v0.14.0*
  `
  
  if (question) {
    return baseReport + `

## 6. 针对性分析

**您的问题**：${question}

**专项分析**：
基于您的问题，我们进行了专项分析。从模拟结果来看，${question.includes('水位') ? '水位变化主要受到人口用水需求和降雨量的双重影响。在当前条件下，建议加强节水措施和开发替代水源。' : '相关指标表现正常，建议持续监控关键参数的变化趋势。'}

**具体建议**：
1. 针对您关注的问题，建议重点监控相关指标
2. 可以尝试调整模拟参数，观察不同条件下的结果
3. 建议结合历史数据进行对比分析
    `
  }
  
  return baseReport
}

// 格式化报告内容
const formatReportContent = (content: string) => {
  return content
    .replace(/\n/g, '<br>')
    .replace(/# (.*)/g, '<h1>$1</h1>')
    .replace(/## (.*)/g, '<h2>$1</h2>')
    .replace(/### (.*)/g, '<h3>$1</h3>')
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    .replace(/- (.*)/g, '<li>$1</li>')
    .replace(/(<li>.*<\/li>)/g, '<ul>$1</ul>')
    .replace(/\| (.*) \|/g, '<tr><td>$1</td></tr>')
    .replace(/(<tr>.*<\/tr>)/g, '<table class="ant-table">$1</table>')
}

// 导出报告
const exportReport = () => {
  if (!reportContent.value) {
    message.warning('没有可导出的报告内容')
    return
  }
  
  const blob = new Blob([reportContent.value], { type: 'text/plain;charset=utf-8' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `分析报告_任务${selectedTaskId.value}_${new Date().toISOString().slice(0, 10)}.txt`
  link.click()
  URL.revokeObjectURL(url)
  
  message.success('报告导出成功')
}

// 加载已完成的任务
const loadCompletedTasks = async () => {
  loadingTasks.value = true
  try {
    // 模拟 API 调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    // 任务数据已在 ref 中定义
  } catch (error) {
    message.error('加载任务列表失败')
  } finally {
    loadingTasks.value = false
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadCompletedTasks()
})
</script>

<style scoped>
.report-content {
  line-height: 1.8;
}

.report-content :deep(h1) {
  color: #1890ff;
  border-bottom: 2px solid #1890ff;
  padding-bottom: 8px;
}

.report-content :deep(h2) {
  color: #52c41a;
  margin-top: 24px;
  margin-bottom: 16px;
}

.report-content :deep(h3) {
  color: #722ed1;
  margin-top: 20px;
  margin-bottom: 12px;
}

.report-content :deep(table) {
  width: 100%;
  margin: 16px 0;
  border-collapse: collapse;
}

.report-content :deep(td) {
  padding: 8px 12px;
  border: 1px solid #d9d9d9;
}

.report-content :deep(ul) {
  margin: 12px 0;
  padding-left: 20px;
}

.report-content :deep(li) {
  margin: 4px 0;
}
</style>
