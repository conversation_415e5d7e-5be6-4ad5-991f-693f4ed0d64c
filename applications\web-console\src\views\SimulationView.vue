<template>
  <div>
    <a-page-header
      title="模拟执行"
      sub-title="创建和配置水资源模拟任务"
    />
    
    <a-row :gutter="16">
      <!-- 左侧：参数配置 -->
      <a-col :span="12">
        <a-card title="模拟参数配置" :bordered="false">
          <a-form
            :model="formData"
            :rules="rules"
            layout="vertical"
            @finish="handleSubmit"
            ref="formRef"
          >
            <a-form-item label="城市名称" name="cityName">
              <a-input
                v-model:value="formData.cityName"
                placeholder="请输入城市名称"
              />
            </a-form-item>
            
            <a-form-item label="人口数量" name="population">
              <a-input-number
                v-model:value="formData.population"
                :min="1000"
                :max="50000000"
                :step="1000"
                style="width: 100%"
                placeholder="请输入人口数量"
              />
            </a-form-item>
            
            <a-form-item label="降雨量 (mm)" name="rainfall">
              <a-input-number
                v-model:value="formData.rainfall"
                :min="0"
                :max="1000"
                :step="0.1"
                style="width: 100%"
                placeholder="请输入降雨量"
              />
            </a-form-item>
            
            <a-form-item label="温度 (°C)" name="temperature">
              <a-input-number
                v-model:value="formData.temperature"
                :min="-50"
                :max="60"
                :step="0.1"
                style="width: 100%"
                placeholder="请输入温度"
              />
            </a-form-item>
            
            <a-form-item label="初始水位 (m)" name="initialWaterLevel">
              <a-input-number
                v-model:value="formData.initialWaterLevel"
                :min="0"
                :max="200"
                :step="0.1"
                style="width: 100%"
                placeholder="请输入初始水位"
              />
            </a-form-item>
            
            <a-form-item>
              <a-space>
                <a-button type="primary" html-type="submit" :loading="submitting">
                  <ExperimentOutlined />
                  开始模拟
                </a-button>
                <a-button @click="resetForm">
                  <ReloadOutlined />
                  重置
                </a-button>
                <a-button @click="loadPreset">
                  <SettingOutlined />
                  加载预设
                </a-button>
              </a-space>
            </a-form-item>
          </a-form>
        </a-card>
      </a-col>
      
      <!-- 右侧：预览和说明 -->
      <a-col :span="12">
        <a-card title="参数预览" :bordered="false" style="margin-bottom: 16px;">
          <a-descriptions :column="1" bordered size="small">
            <a-descriptions-item label="城市名称">
              {{ formData.cityName || '未设置' }}
            </a-descriptions-item>
            <a-descriptions-item label="人口数量">
              {{ formData.population ? formData.population.toLocaleString() : '未设置' }}
            </a-descriptions-item>
            <a-descriptions-item label="降雨量">
              {{ formData.rainfall ? `${formData.rainfall} mm` : '未设置' }}
            </a-descriptions-item>
            <a-descriptions-item label="温度">
              {{ formData.temperature ? `${formData.temperature} °C` : '未设置' }}
            </a-descriptions-item>
            <a-descriptions-item label="初始水位">
              {{ formData.initialWaterLevel ? `${formData.initialWaterLevel} m` : '未设置' }}
            </a-descriptions-item>
          </a-descriptions>
        </a-card>
        
        <a-card title="模拟说明" :bordered="false">
          <a-alert
            message="模拟参数说明"
            type="info"
            show-icon
            style="margin-bottom: 16px;"
          />
          
          <a-typography-paragraph>
            <ul>
              <li><strong>城市名称</strong>：用于标识模拟任务的城市</li>
              <li><strong>人口数量</strong>：影响用水需求，范围：1,000 - 50,000,000</li>
              <li><strong>降雨量</strong>：影响水资源供给，单位：毫米</li>
              <li><strong>温度</strong>：影响蒸发率和用水需求，单位：摄氏度</li>
              <li><strong>初始水位</strong>：模拟开始时的水位，单位：米</li>
            </ul>
          </a-typography-paragraph>
          
          <a-divider />
          
          <a-typography-paragraph>
            <a-tag color="blue">提示</a-tag>
            模拟完成后，您可以在"任务管理"页面查看结果，或在"分析报告"页面获取详细分析。
          </a-typography-paragraph>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import {
  ExperimentOutlined,
  ReloadOutlined,
  SettingOutlined
} from '@ant-design/icons-vue'

const router = useRouter()
const formRef = ref()
const submitting = ref(false)

// 表单数据
const formData = reactive({
  cityName: '',
  population: undefined,
  rainfall: undefined,
  temperature: undefined,
  initialWaterLevel: 100
})

// 表单验证规则
const rules = {
  cityName: [
    { required: true, message: '请输入城市名称', trigger: 'blur' }
  ],
  population: [
    { required: true, message: '请输入人口数量', trigger: 'blur' },
    { type: 'number', min: 1000, max: 50000000, message: '人口数量应在 1,000 - 50,000,000 之间', trigger: 'blur' }
  ],
  rainfall: [
    { required: true, message: '请输入降雨量', trigger: 'blur' },
    { type: 'number', min: 0, max: 1000, message: '降雨量应在 0 - 1000 mm 之间', trigger: 'blur' }
  ],
  temperature: [
    { required: true, message: '请输入温度', trigger: 'blur' },
    { type: 'number', min: -50, max: 60, message: '温度应在 -50 - 60 °C 之间', trigger: 'blur' }
  ],
  initialWaterLevel: [
    { required: true, message: '请输入初始水位', trigger: 'blur' },
    { type: 'number', min: 0, max: 200, message: '初始水位应在 0 - 200 m 之间', trigger: 'blur' }
  ]
}

// 提交表单
const handleSubmit = async () => {
  try {
    submitting.value = true
    
    // 模拟 API 调用
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    message.success('模拟任务创建成功！')
    
    // 跳转到任务管理页面
    router.push('/tasks')
    
  } catch (error) {
    message.error('创建模拟任务失败')
  } finally {
    submitting.value = false
  }
}

// 重置表单
const resetForm = () => {
  formRef.value?.resetFields()
  Object.assign(formData, {
    cityName: '',
    population: undefined,
    rainfall: undefined,
    temperature: undefined,
    initialWaterLevel: 100
  })
  message.info('表单已重置')
}

// 加载预设参数
const loadPreset = () => {
  const presets = [
    {
      name: '北京典型参数',
      cityName: '北京',
      population: 21540000,
      rainfall: 600,
      temperature: 25,
      initialWaterLevel: 100
    },
    {
      name: '上海典型参数',
      cityName: '上海',
      population: 24870000,
      rainfall: 1200,
      temperature: 28,
      initialWaterLevel: 95
    },
    {
      name: '小城市参数',
      cityName: 'Rivertown',
      population: 50000,
      rainfall: 110,
      temperature: 28.5,
      initialWaterLevel: 80
    }
  ]
  
  // 随机选择一个预设
  const preset = presets[Math.floor(Math.random() * presets.length)]
  
  Object.assign(formData, preset)
  message.success(`已加载预设：${preset.name}`)
}
</script>

<style scoped>
.ant-form-item {
  margin-bottom: 16px;
}

.ant-descriptions {
  background: #fafafa;
}
</style>
