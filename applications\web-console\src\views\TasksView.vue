<template>
  <div>
    <a-page-header
      title="任务管理"
      sub-title="查看和管理所有模拟任务"
    />
    
    <!-- 搜索和操作栏 -->
    <a-card style="margin-bottom: 16px;">
      <a-row :gutter="16" align="middle">
        <a-col :span="8">
          <a-input-search
            v-model:value="searchText"
            placeholder="搜索任务名称或城市"
            @search="handleSearch"
            allow-clear
          />
        </a-col>
        
        <a-col :span="6">
          <a-select
            v-model:value="statusFilter"
            placeholder="筛选状态"
            style="width: 100%"
            allow-clear
            @change="handleStatusFilter"
          >
            <a-select-option value="PENDING">等待中</a-select-option>
            <a-select-option value="RUNNING">运行中</a-select-option>
            <a-select-option value="COMPLETED">已完成</a-select-option>
            <a-select-option value="FAILED">失败</a-select-option>
          </a-select>
        </a-col>
        
        <a-col :span="10" style="text-align: right;">
          <a-space>
            <a-button @click="refreshTasks">
              <ReloadOutlined />
              刷新
            </a-button>
            <a-button type="primary" @click="$router.push('/simulation')">
              <PlusOutlined />
              创建任务
            </a-button>
          </a-space>
        </a-col>
      </a-row>
    </a-card>
    
    <!-- 任务列表 -->
    <a-card>
      <a-table
        :columns="columns"
        :data-source="filteredTasks"
        :loading="loading"
        :pagination="pagination"
        row-key="id"
        @change="handleTableChange"
      >
        <!-- 状态列自定义渲染 -->
        <template #status="{ text }">
          <a-tag :color="getStatusColor(text)">
            {{ getStatusText(text) }}
          </a-tag>
        </template>
        
        <!-- 进度列自定义渲染 -->
        <template #progress="{ text, record }">
          <a-progress
            :percent="text"
            :status="record.status === 'FAILED' ? 'exception' : 'normal'"
            size="small"
          />
        </template>
        
        <!-- 操作列自定义渲染 -->
        <template #action="{ record }">
          <a-space>
            <a-button type="link" size="small" @click="viewTask(record)">
              查看
            </a-button>
            <a-button 
              type="link" 
              size="small" 
              @click="viewReport(record)"
              :disabled="record.status !== 'COMPLETED'"
            >
              报告
            </a-button>
            <a-popconfirm
              title="确定要删除这个任务吗？"
              @confirm="deleteTask(record.id)"
            >
              <a-button type="link" size="small" danger>
                删除
              </a-button>
            </a-popconfirm>
          </a-space>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  ReloadOutlined,
  PlusOutlined
} from '@ant-design/icons-vue'

// 表格列定义
const columns = [
  {
    title: 'ID',
    dataIndex: 'id',
    key: 'id',
    width: 80
  },
  {
    title: '任务名称',
    dataIndex: 'name',
    key: 'name'
  },
  {
    title: '城市',
    dataIndex: 'cityName',
    key: 'cityName',
    width: 120
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100,
    slots: { customRender: 'status' }
  },
  {
    title: '进度',
    dataIndex: 'progress',
    key: 'progress',
    width: 120,
    slots: { customRender: 'progress' }
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 180
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
    slots: { customRender: 'action' }
  }
]

// 响应式数据
const loading = ref(false)
const searchText = ref('')
const statusFilter = ref('')

const tasks = ref([
  {
    id: 1,
    name: '北京水资源模拟',
    cityName: '北京',
    status: 'COMPLETED',
    progress: 100,
    createTime: '2025-07-17 14:30:00'
  },
  {
    id: 2,
    name: 'Rivertown水资源模拟',
    cityName: 'Rivertown',
    status: 'RUNNING',
    progress: 65,
    createTime: '2025-07-17 15:45:00'
  },
  {
    id: 3,
    name: '上海水资源模拟',
    cityName: '上海',
    status: 'PENDING',
    progress: 0,
    createTime: '2025-07-17 16:20:00'
  }
])

const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 计算属性：过滤后的任务列表
const filteredTasks = computed(() => {
  let result = tasks.value
  
  // 按搜索文本过滤
  if (searchText.value) {
    result = result.filter(task => 
      task.name.includes(searchText.value) || 
      task.cityName.includes(searchText.value)
    )
  }
  
  // 按状态过滤
  if (statusFilter.value) {
    result = result.filter(task => task.status === statusFilter.value)
  }
  
  pagination.value.total = result.length
  return result
})

// 获取状态颜色
const getStatusColor = (status: string) => {
  switch (status) {
    case 'COMPLETED':
      return 'green'
    case 'RUNNING':
      return 'blue'
    case 'PENDING':
      return 'orange'
    case 'FAILED':
      return 'red'
    default:
      return 'default'
  }
}

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case 'COMPLETED':
      return '已完成'
    case 'RUNNING':
      return '运行中'
    case 'PENDING':
      return '等待中'
    case 'FAILED':
      return '失败'
    default:
      return '未知'
  }
}

// 事件处理函数
const handleSearch = () => {
  // 搜索逻辑已在计算属性中处理
  message.info(`搜索: ${searchText.value}`)
}

const handleStatusFilter = () => {
  // 过滤逻辑已在计算属性中处理
}

const handleTableChange = (pag: any) => {
  pagination.value.current = pag.current
  pagination.value.pageSize = pag.pageSize
}

const refreshTasks = async () => {
  loading.value = true
  try {
    // 模拟 API 调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    message.success('任务列表刷新成功')
  } catch (error) {
    message.error('刷新失败')
  } finally {
    loading.value = false
  }
}

const viewTask = (task: any) => {
  message.info(`查看任务: ${task.name}`)
  // 这里可以打开任务详情弹窗或跳转到详情页
}

const viewReport = (task: any) => {
  message.info(`查看报告: ${task.name}`)
  // 跳转到报告页面
}

const deleteTask = async (taskId: number) => {
  try {
    // 模拟删除 API 调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 从列表中移除
    const index = tasks.value.findIndex(task => task.id === taskId)
    if (index > -1) {
      tasks.value.splice(index, 1)
      message.success('任务删除成功')
    }
  } catch (error) {
    message.error('删除失败')
  }
}

// 组件挂载时加载数据
onMounted(() => {
  refreshTasks()
})
</script>
