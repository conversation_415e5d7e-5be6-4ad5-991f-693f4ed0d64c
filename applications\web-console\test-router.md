# Vue Router 配置测试指南

## 路由配置概览

### 已配置的路由

1. **登录页面** - `/login`
   - 组件: `LoginView.vue`
   - 无需认证
   - 已登录用户访问时会重定向到 `/dashboard`

2. **主布局** - `/`
   - 组件: `MainLayout.vue`
   - 需要认证
   - 包含侧边栏导航和顶部导航栏

3. **子路由**（在 MainLayout 下）：
   - `/dashboard` - 仪表板（默认页面）
   - `/tasks` - 任务管理
   - `/simulation` - 模拟执行
   - `/reports` - 分析报告

### 路由守卫功能

- **全局前置守卫**: 检查 `localStorage` 中的 `authToken`
- **未登录用户**: 访问需要认证的页面时重定向到 `/login`
- **已登录用户**: 访问登录页时重定向到 `/dashboard`
- **404处理**: 未匹配的路由重定向到 `/dashboard`

## 测试步骤

### 1. 启动开发服务器
```bash
cd applications/web-console
npm install
npm run dev
```

### 2. 测试未登录状态
1. 清除浏览器 localStorage: `localStorage.clear()`
2. 访问 `http://localhost:5173/` - 应该重定向到 `/login`
3. 访问 `http://localhost:5173/dashboard` - 应该重定向到 `/login`
4. 访问 `http://localhost:5173/tasks` - 应该重定向到 `/login`

### 3. 测试登录功能
1. 在登录页面输入任意用户名和密码
2. 点击登录按钮
3. 应该设置 `authToken` 到 localStorage
4. 应该重定向到 `/dashboard`

### 4. 测试已登录状态
1. 手动设置 token: `localStorage.setItem('authToken', 'test-token')`
2. 访问 `http://localhost:5173/login` - 应该重定向到 `/dashboard`
3. 访问各个页面应该正常显示：
   - `/dashboard` - 仪表板页面
   - `/tasks` - 任务管理页面
   - `/simulation` - 模拟执行页面
   - `/reports` - 分析报告页面

### 5. 测试导航功能
1. 在主布局中点击侧边栏菜单项
2. 检查 URL 是否正确变化
3. 检查页面内容是否正确显示
4. 检查面包屑导航是否正确更新

### 6. 测试退出登录
1. 点击右上角用户菜单中的"退出登录"
2. 应该清除 localStorage 中的 token
3. 应该重定向到登录页面

## 可能的问题和解决方案

### 问题1: 路由组件未找到
**症状**: 控制台报错 "Failed to resolve component"
**解决**: 检查组件文件是否存在，路径是否正确

### 问题2: 路由守卫不生效
**症状**: 未登录时可以访问受保护页面
**解决**: 检查 localStorage 中的 key 是否为 `authToken`

### 问题3: 页面刷新后丢失状态
**症状**: 刷新页面后重定向到登录页
**解决**: 检查 token 是否正确存储在 localStorage

### 问题4: 样式问题
**症状**: 页面布局异常
**解决**: 检查 Ant Design Vue 是否正确导入

## 调试技巧

1. **开启路由调试**:
   ```javascript
   // 在 router/index.ts 中已添加 console.log
   // 查看浏览器控制台的路由守卫日志
   ```

2. **检查 localStorage**:
   ```javascript
   // 在浏览器控制台中执行
   console.log(localStorage.getItem('authToken'))
   ```

3. **手动设置 token**:
   ```javascript
   // 用于测试已登录状态
   localStorage.setItem('authToken', 'test-token')
   ```

4. **清除 token**:
   ```javascript
   // 用于测试未登录状态
   localStorage.removeItem('authToken')
   ```
