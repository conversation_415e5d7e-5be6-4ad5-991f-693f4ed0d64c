#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔧 前端项目启动检查工具');
console.log('================================');

// 检查 Node.js 版本
function checkNodeVersion() {
    const version = process.version;
    const majorVersion = parseInt(version.slice(1).split('.')[0]);
    
    console.log(`📋 Node.js 版本: ${version}`);
    
    if (majorVersion < 16) {
        console.log('❌ Node.js 版本过低，推荐使用 v16 或更高版本');
        return false;
    } else {
        console.log('✅ Node.js 版本符合要求');
        return true;
    }
}

// 检查关键文件
function checkFiles() {
    const requiredFiles = [
        'package.json',
        'vite.config.ts',
        'index.html',
        'src/main.ts',
        'src/App.vue',
        'src/router/index.ts',
        'src/views/LoginView.vue',
        'src/layouts/MainLayout.vue'
    ];
    
    console.log('\n📁 检查关键文件:');
    let allFilesExist = true;
    
    requiredFiles.forEach(file => {
        if (fs.existsSync(file)) {
            console.log(`✅ ${file}`);
        } else {
            console.log(`❌ ${file} - 文件不存在`);
            allFilesExist = false;
        }
    });
    
    return allFilesExist;
}

// 检查依赖
function checkDependencies() {
    console.log('\n📦 检查依赖安装:');
    
    if (!fs.existsSync('node_modules')) {
        console.log('❌ node_modules 目录不存在');
        return false;
    }
    
    const requiredDeps = [
        'vue',
        'vue-router',
        'ant-design-vue',
        'axios'
    ];
    
    let allDepsExist = true;
    
    requiredDeps.forEach(dep => {
        const depPath = path.join('node_modules', dep);
        if (fs.existsSync(depPath)) {
            console.log(`✅ ${dep}`);
        } else {
            console.log(`❌ ${dep} - 依赖未安装`);
            allDepsExist = false;
        }
    });
    
    return allDepsExist;
}

// 检查语法错误
function checkSyntax() {
    console.log('\n🔍 检查语法错误:');
    
    try {
        // 检查 TypeScript 编译
        execSync('npx vue-tsc --noEmit', { stdio: 'pipe' });
        console.log('✅ TypeScript 编译通过');
        return true;
    } catch (error) {
        console.log('❌ TypeScript 编译错误:');
        console.log(error.stdout?.toString() || error.message);
        return false;
    }
}

// 自动修复
function autoFix() {
    console.log('\n🔧 尝试自动修复:');
    
    try {
        // 重新安装依赖
        console.log('📦 重新安装依赖...');
        execSync('npm install', { stdio: 'inherit' });
        console.log('✅ 依赖安装完成');
        
        return true;
    } catch (error) {
        console.log('❌ 自动修复失败:', error.message);
        return false;
    }
}

// 启动开发服务器
function startDev() {
    console.log('\n🚀 启动开发服务器...');
    
    try {
        execSync('npm run dev', { stdio: 'inherit' });
    } catch (error) {
        console.log('❌ 启动失败:', error.message);
        return false;
    }
}

// 主函数
function main() {
    let hasIssues = false;
    
    // 检查 Node.js 版本
    if (!checkNodeVersion()) {
        hasIssues = true;
    }
    
    // 检查文件
    if (!checkFiles()) {
        hasIssues = true;
    }
    
    // 检查依赖
    if (!checkDependencies()) {
        hasIssues = true;
        console.log('\n💡 尝试运行: npm install');
    }
    
    // 如果有问题，尝试自动修复
    if (hasIssues) {
        console.log('\n⚠️  发现问题，尝试自动修复...');
        if (autoFix()) {
            console.log('✅ 自动修复完成，重新检查...');
            // 重新检查依赖
            if (!checkDependencies()) {
                console.log('❌ 自动修复失败，请手动解决问题');
                return;
            }
        } else {
            console.log('❌ 自动修复失败，请手动解决问题');
            return;
        }
    }
    
    // 检查语法
    if (!checkSyntax()) {
        console.log('\n❌ 存在语法错误，请修复后重试');
        return;
    }
    
    console.log('\n✅ 所有检查通过！');
    console.log('\n📋 接下来的步骤:');
    console.log('1. 运行 npm run dev 启动开发服务器');
    console.log('2. 打开浏览器访问 http://localhost:5173');
    console.log('3. 如果页面空白，访问 http://localhost:5173/debug.html 进行诊断');
    
    // 询问是否启动开发服务器
    const readline = require('readline');
    const rl = readline.createInterface({
        input: process.stdin,
        output: process.stdout
    });
    
    rl.question('\n是否现在启动开发服务器? (y/n): ', (answer) => {
        if (answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes') {
            rl.close();
            startDev();
        } else {
            console.log('👋 稍后可以运行 npm run dev 启动开发服务器');
            rl.close();
        }
    });
}

// 运行主函数
main();
