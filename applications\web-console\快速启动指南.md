# 🚀 快速启动指南

## 📋 前置条件检查

- [ ] Node.js 已安装 (推荐 v16+)
- [ ] npm 或 yarn 已安装
- [ ] Kubernetes 集群可访问 (可选)

## 🔧 启动步骤

### 1. 启动前端开发服务器

```bash
# 进入项目目录
cd applications/web-console

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

**预期输出**:
```
  VITE v7.0.4  ready in 1234 ms

  ➜  Local:   http://localhost:5173/
  ➜  Network: use --host to expose
```

### 2. 建立 API 网关端口转发 (可选)

**新开一个终端**:
```bash
# 检查服务状态
kubectl get svc | grep gateway

# 建立端口转发
kubectl port-forward svc/api-gateway-svc 8080:8080
```

**如果没有 Kubernetes 环境**，可以跳过此步骤，前端仍然可以正常测试路由和认证功能。

### 3. 打开浏览器测试

访问: `http://localhost:5173`

## ✅ 快速验证

### 验证 1: 自动重定向 (30秒)

1. **清除浏览器缓存**: 按 `Ctrl+Shift+R` 或 `Cmd+Shift+R`
2. **打开开发者工具**: 按 `F12`
3. **清除 localStorage**: 在控制台执行 `localStorage.clear()`
4. **访问首页**: 地址栏输入 `http://localhost:5173/`

**✅ 期望结果**: 自动跳转到 `/login` 页面

### 验证 2: 登录功能 (1分钟)

1. **输入登录信息**:
   - 用户名: `admin` (任意)
   - 密码: `123456` (任意)
2. **点击登录按钮**
3. **观察页面变化**

**✅ 期望结果**: 
- 显示"登录成功"提示
- 跳转到 `/dashboard` 页面
- 显示完整的后台布局

### 验证 3: 认证状态保持 (30秒)

1. **刷新页面**: 按 `F5`
2. **检查页面状态**

**✅ 期望结果**: 
- 仍然在 `/dashboard` 页面
- 不会跳转到登录页

### 验证 4: 菜单导航 (1分钟)

1. **点击侧边栏菜单**:
   - 任务管理
   - 模拟执行
   - 分析报告
2. **观察页面变化**

**✅ 期望结果**: 
- URL 正确变化
- 页面内容正确显示
- 菜单项正确高亮

### 验证 5: 退出登录 (30秒)

1. **点击右上角用户头像**
2. **选择"退出登录"**
3. **观察页面变化**

**✅ 期望结果**: 
- 显示"退出登录成功"提示
- 跳转到 `/login` 页面
- 再次访问 `/dashboard` 会重定向到登录页

## 🛠️ 使用验证工具

### 方法 1: 使用验证脚本

1. **打开验证工具**: 在浏览器中打开 `验证脚本.html`
2. **使用自动化测试**: 点击各种测试按钮
3. **查看测试结果**: 观察状态变化

### 方法 2: 手动验证

参考 `验证说明文档.md` 中的详细测试用例

## 🐛 常见问题快速解决

### 问题 1: 页面空白

**解决方法**:
```bash
# 重新安装依赖
rm -rf node_modules package-lock.json
npm install
npm run dev
```

### 问题 2: 路由不工作

**检查方法**:
1. 打开浏览器控制台
2. 查看是否有错误信息
3. 检查 `localStorage.getItem('authToken')`

**解决方法**:
```javascript
// 手动设置 token 测试
localStorage.setItem('authToken', 'test-token')
// 然后刷新页面
```

### 问题 3: 样式异常

**解决方法**:
1. 检查 Ant Design Vue 是否正确安装
2. 清除浏览器缓存
3. 重启开发服务器

### 问题 4: API 调用失败

**临时解决**:
- 前端路由和认证功能不依赖后端 API
- 可以先测试前端功能，后续再连接后端

## 📞 获取帮助

### 调试信息收集

如果遇到问题，请收集以下信息：

1. **浏览器控制台错误**:
   ```
   按 F12 -> Console 标签页 -> 截图错误信息
   ```

2. **网络请求状态**:
   ```
   按 F12 -> Network 标签页 -> 查看失败的请求
   ```

3. **localStorage 状态**:
   ```javascript
   console.log(localStorage.getItem('authToken'))
   console.log(window.location.pathname)
   ```

4. **版本信息**:
   ```bash
   node --version
   npm --version
   ```

### 重置环境

如果问题严重，可以完全重置：

```bash
# 清除所有依赖和缓存
rm -rf node_modules
rm -rf .vite
rm package-lock.json

# 重新安装
npm install

# 清除浏览器数据
# 在浏览器中: 设置 -> 隐私和安全 -> 清除浏览数据
```

## 🎯 成功标准

当以下所有项目都 ✅ 时，说明前端配置成功：

- [ ] 开发服务器正常启动
- [ ] 未登录时自动重定向到登录页
- [ ] 登录功能正常工作
- [ ] 已登录时访问登录页重定向到仪表板
- [ ] 页面刷新后认证状态保持
- [ ] 菜单导航正常工作
- [ ] 退出登录功能正常

**预计总验证时间**: 5-10 分钟

## 📈 下一步

验证成功后，可以继续进行：

1. **连接真实的后端 API**
2. **完善各个页面的功能**
3. **添加更多的业务逻辑**
4. **优化用户体验**
