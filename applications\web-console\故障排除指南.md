# 🔧 故障排除指南

## 🚨 页面空白问题解决方案

### 步骤 1: 基础检查

#### 1.1 检查开发服务器状态
```bash
cd applications/web-console
npm run dev
```

**正常输出应该是**:
```
  VITE v7.0.4  ready in 1234 ms

  ➜  Local:   http://localhost:5173/
  ➜  Network: use --host to expose
```

#### 1.2 检查浏览器控制台
1. 打开浏览器 (推荐 Chrome)
2. 访问 `http://localhost:5173`
3. 按 `F12` 打开开发者工具
4. 查看 `Console` 标签页是否有错误

**常见错误类型**:
- ❌ `Failed to resolve module` - 模块导入错误
- ❌ `Unexpected token` - 语法错误
- ❌ `Cannot read property` - 组件属性错误
- ❌ `404 Not Found` - 文件路径错误

### 步骤 2: 使用调试工具

#### 2.1 访问调试页面
打开 `http://localhost:5173/debug.html`

这个页面会自动检查：
- ✅ localStorage 可用性
- ✅ 网络连接状态
- ✅ 认证状态
- ✅ 系统信息

#### 2.2 手动检查关键文件
```bash
# 检查关键文件是否存在
ls -la src/
ls -la src/views/
ls -la src/layouts/
ls -la src/router/
```

### 步骤 3: 重新安装依赖

#### 3.1 完全清理
```bash
# 删除依赖和缓存
rm -rf node_modules
rm -rf .vite
rm package-lock.json

# 重新安装
npm install
```

#### 3.2 检查依赖版本
```bash
npm list vue
npm list vue-router
npm list ant-design-vue
```

**期望版本**:
- vue: ^3.5.17
- vue-router: ^4.4.5
- ant-design-vue: ^4.2.6

### 步骤 4: 逐步验证

#### 4.1 测试基础 Vue 应用
创建临时测试文件 `test.html`:
```html
<!DOCTYPE html>
<html>
<head>
    <title>Vue Test</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
</head>
<body>
    <div id="app">{{ message }}</div>
    <script>
        const { createApp } = Vue
        createApp({
            data() {
                return { message: 'Vue 工作正常!' }
            }
        }).mount('#app')
    </script>
</body>
</html>
```

#### 4.2 测试路由功能
在浏览器控制台执行:
```javascript
// 检查路由是否加载
console.log(window.location.pathname)

// 手动设置认证
localStorage.setItem('authToken', 'test-token')

// 尝试跳转
window.location.href = '/dashboard'
```

### 步骤 5: 常见问题解决

#### 问题 1: 模块导入错误
**错误信息**: `Failed to resolve module './components/xxx'`

**解决方法**:
1. 检查文件路径是否正确
2. 检查文件扩展名 (.vue, .ts, .js)
3. 检查大小写是否匹配

#### 问题 2: TypeScript 编译错误
**错误信息**: `Type 'xxx' is not assignable to type 'yyy'`

**解决方法**:
```bash
# 检查 TypeScript 配置
cat tsconfig.json

# 重新编译
npm run build
```

#### 问题 3: Ant Design 样式问题
**症状**: 组件显示但样式异常

**解决方法**:
检查 `main.ts` 中的 CSS 导入:
```typescript
import 'ant-design-vue/dist/reset.css'
```

#### 问题 4: 路由守卫死循环
**症状**: 页面不断刷新或重定向

**解决方法**:
```javascript
// 清除所有 localStorage
localStorage.clear()

// 手动访问登录页
window.location.href = '/login'
```

### 步骤 6: 环境检查

#### 6.1 Node.js 版本
```bash
node --version  # 应该 >= 16.0.0
npm --version   # 应该 >= 8.0.0
```

#### 6.2 浏览器兼容性
推荐使用：
- ✅ Chrome 90+
- ✅ Firefox 88+
- ✅ Safari 14+
- ✅ Edge 90+

#### 6.3 网络环境
```bash
# 检查端口是否被占用
netstat -an | grep 5173

# 检查防火墙设置
# Windows: 检查 Windows Defender 防火墙
# macOS: 检查系统偏好设置 > 安全性与隐私 > 防火墙
```

### 步骤 7: 创建最小化测试

#### 7.1 创建简单的 App.vue
```vue
<template>
  <div>
    <h1>测试页面</h1>
    <p>如果你能看到这个，说明 Vue 基础功能正常</p>
    <button @click="testClick">点击测试</button>
  </div>
</template>

<script setup lang="ts">
const testClick = () => {
  alert('Vue 事件处理正常!')
}
</script>
```

#### 7.2 简化路由配置
临时修改 `router/index.ts`:
```typescript
import { createRouter, createWebHistory } from 'vue-router'

const routes = [
  {
    path: '/',
    component: () => import('../views/LoginView.vue')
  }
]

export default createRouter({
  history: createWebHistory(),
  routes
})
```

### 步骤 8: 获取帮助

#### 8.1 收集错误信息
```bash
# 生成错误报告
npm run dev > error.log 2>&1

# 查看错误日志
cat error.log
```

#### 8.2 系统信息收集
```javascript
// 在浏览器控制台执行
console.log({
  userAgent: navigator.userAgent,
  url: window.location.href,
  localStorage: !!window.localStorage,
  sessionStorage: !!window.sessionStorage,
  fetch: !!window.fetch
})
```

#### 8.3 网络请求检查
1. 打开开发者工具
2. 切换到 `Network` 标签页
3. 刷新页面
4. 查看是否有失败的请求 (红色)

### 🎯 成功标准

当以下所有项目都正常时，页面应该能正常显示：

- [ ] 开发服务器正常启动
- [ ] 浏览器控制台无错误
- [ ] 能看到登录页面
- [ ] 能输入用户名密码
- [ ] 点击登录后能跳转到仪表板

### 📞 紧急恢复方案

如果所有方法都无效，使用以下命令完全重置：

```bash
# 1. 备份当前代码
cp -r applications/web-console applications/web-console-backup

# 2. 重新克隆或下载项目
# (如果有 git 仓库)

# 3. 重新安装所有依赖
cd applications/web-console
rm -rf node_modules package-lock.json .vite
npm cache clean --force
npm install

# 4. 重新启动
npm run dev
```

### 📋 检查清单

在寻求帮助前，请确认已完成：

- [ ] 重新安装了依赖
- [ ] 检查了浏览器控制台错误
- [ ] 尝试了不同的浏览器
- [ ] 检查了网络连接
- [ ] 访问了调试页面
- [ ] 清除了浏览器缓存
- [ ] 重启了开发服务器
