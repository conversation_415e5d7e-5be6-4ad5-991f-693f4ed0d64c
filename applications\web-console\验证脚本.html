<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端认证流程验证工具</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .container {
            background: #f5f5f5;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .test-item {
            background: white;
            padding: 15px;
            margin: 10px 0;
            border-radius: 6px;
            border-left: 4px solid #1890ff;
        }
        .success {
            border-left-color: #52c41a;
            background: #f6ffed;
        }
        .error {
            border-left-color: #ff4d4f;
            background: #fff2f0;
        }
        .warning {
            border-left-color: #faad14;
            background: #fffbe6;
        }
        button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #40a9ff;
        }
        button.danger {
            background: #ff4d4f;
        }
        button.danger:hover {
            background: #ff7875;
        }
        .code {
            background: #f0f0f0;
            padding: 10px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
        }
        .status {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
        .status.success {
            background: #52c41a;
            color: white;
        }
        .status.error {
            background: #ff4d4f;
            color: white;
        }
        .status.pending {
            background: #faad14;
            color: white;
        }
    </style>
</head>
<body>
    <h1>🧪 前端认证流程验证工具</h1>
    <p>这个工具可以帮助你快速验证前端认证流程是否正常工作。</p>

    <div class="container">
        <h2>📋 当前状态检查</h2>
        
        <div class="test-item" id="token-status">
            <h3>🔑 Token 状态</h3>
            <p>当前 localStorage 中的 authToken: <span id="current-token">检查中...</span></p>
            <button onclick="checkToken()">刷新检查</button>
            <button onclick="clearToken()" class="danger">清除 Token</button>
            <button onclick="setTestToken()">设置测试 Token</button>
        </div>

        <div class="test-item" id="url-status">
            <h3>🌐 当前页面信息</h3>
            <p>当前 URL: <span id="current-url">-</span></p>
            <p>当前路径: <span id="current-path">-</span></p>
            <button onclick="checkUrl()">刷新检查</button>
        </div>
    </div>

    <div class="container">
        <h2>🧪 自动化测试</h2>
        
        <div class="test-item">
            <h3>测试 1: 清除 Token 并跳转</h3>
            <p>清除 localStorage 中的 Token，然后跳转到首页，验证是否重定向到登录页。</p>
            <button onclick="testUnauthorizedRedirect()">执行测试</button>
            <div id="test1-result"></div>
        </div>

        <div class="test-item">
            <h3>测试 2: 设置 Token 并访问登录页</h3>
            <p>设置测试 Token，然后访问登录页，验证是否重定向到仪表板。</p>
            <button onclick="testAuthorizedRedirect()">执行测试</button>
            <div id="test2-result"></div>
        </div>

        <div class="test-item">
            <h3>测试 3: 页面导航测试</h3>
            <p>测试各个页面的导航是否正常工作。</p>
            <button onclick="testNavigation()">执行测试</button>
            <div id="test3-result"></div>
        </div>
    </div>

    <div class="container">
        <h2>🔧 手动操作工具</h2>
        
        <div class="test-item">
            <h3>快速导航</h3>
            <button onclick="goToPage('/')">首页</button>
            <button onclick="goToPage('/login')">登录页</button>
            <button onclick="goToPage('/dashboard')">仪表板</button>
            <button onclick="goToPage('/tasks')">任务管理</button>
            <button onclick="goToPage('/simulation')">模拟执行</button>
            <button onclick="goToPage('/reports')">分析报告</button>
        </div>

        <div class="test-item">
            <h3>Token 操作</h3>
            <div class="code">
                <p>手动操作命令:</p>
                <p>清除 Token: <code>localStorage.clear()</code></p>
                <p>设置 Token: <code>localStorage.setItem('authToken', 'test-token')</code></p>
                <p>检查 Token: <code>localStorage.getItem('authToken')</code></p>
            </div>
        </div>
    </div>

    <div class="container">
        <h2>📊 验证检查清单</h2>
        <div id="checklist">
            <div class="test-item">
                <label><input type="checkbox" id="check1"> 前端开发服务器正常启动</label>
            </div>
            <div class="test-item">
                <label><input type="checkbox" id="check2"> 未登录用户自动重定向到 /login</label>
            </div>
            <div class="test-item">
                <label><input type="checkbox" id="check3"> 登录功能正常工作</label>
            </div>
            <div class="test-item">
                <label><input type="checkbox" id="check4"> 已登录用户访问 /login 重定向到 /dashboard</label>
            </div>
            <div class="test-item">
                <label><input type="checkbox" id="check5"> 页面刷新后认证状态保持</label>
            </div>
            <div class="test-item">
                <label><input type="checkbox" id="check6"> 菜单导航正常工作</label>
            </div>
            <div class="test-item">
                <label><input type="checkbox" id="check7"> 退出登录功能正常</label>
            </div>
        </div>
        <button onclick="generateReport()">生成测试报告</button>
        <div id="report-result"></div>
    </div>

    <script>
        // 检查 Token 状态
        function checkToken() {
            const token = localStorage.getItem('authToken');
            const tokenElement = document.getElementById('current-token');
            if (token) {
                tokenElement.innerHTML = `<span class="status success">存在</span> ${token.substring(0, 20)}...`;
            } else {
                tokenElement.innerHTML = `<span class="status error">不存在</span>`;
            }
        }

        // 清除 Token
        function clearToken() {
            localStorage.removeItem('authToken');
            checkToken();
            alert('Token 已清除');
        }

        // 设置测试 Token
        function setTestToken() {
            const testToken = 'test-token-' + Date.now();
            localStorage.setItem('authToken', testToken);
            checkToken();
            alert('测试 Token 已设置');
        }

        // 检查当前 URL
        function checkUrl() {
            document.getElementById('current-url').textContent = window.location.href;
            document.getElementById('current-path').textContent = window.location.pathname;
        }

        // 跳转到指定页面
        function goToPage(path) {
            window.location.href = path;
        }

        // 测试未授权重定向
        function testUnauthorizedRedirect() {
            const resultDiv = document.getElementById('test1-result');
            resultDiv.innerHTML = '<span class="status pending">执行中...</span>';
            
            // 清除 Token
            localStorage.removeItem('authToken');
            
            // 等待一下然后跳转
            setTimeout(() => {
                window.location.href = '/dashboard';
            }, 1000);
        }

        // 测试已授权重定向
        function testAuthorizedRedirect() {
            const resultDiv = document.getElementById('test2-result');
            resultDiv.innerHTML = '<span class="status pending">执行中...</span>';
            
            // 设置 Token
            localStorage.setItem('authToken', 'test-token-' + Date.now());
            
            // 等待一下然后跳转
            setTimeout(() => {
                window.location.href = '/login';
            }, 1000);
        }

        // 测试导航
        function testNavigation() {
            const resultDiv = document.getElementById('test3-result');
            const pages = ['/dashboard', '/tasks', '/simulation', '/reports'];
            let currentIndex = 0;
            
            resultDiv.innerHTML = '<span class="status pending">测试导航中...</span>';
            
            // 确保有 Token
            if (!localStorage.getItem('authToken')) {
                localStorage.setItem('authToken', 'test-token-' + Date.now());
            }
            
            function testNextPage() {
                if (currentIndex < pages.length) {
                    resultDiv.innerHTML = `<span class="status pending">测试页面: ${pages[currentIndex]}</span>`;
                    window.location.href = pages[currentIndex];
                    currentIndex++;
                } else {
                    resultDiv.innerHTML = '<span class="status success">导航测试完成</span>';
                }
            }
            
            testNextPage();
        }

        // 生成测试报告
        function generateReport() {
            const checkboxes = document.querySelectorAll('#checklist input[type="checkbox"]');
            let passed = 0;
            let total = checkboxes.length;
            
            checkboxes.forEach(checkbox => {
                if (checkbox.checked) passed++;
            });
            
            const percentage = Math.round((passed / total) * 100);
            const reportDiv = document.getElementById('report-result');
            
            let status = 'error';
            if (percentage >= 100) status = 'success';
            else if (percentage >= 70) status = 'warning';
            
            reportDiv.innerHTML = `
                <div class="test-item ${status}">
                    <h3>测试报告</h3>
                    <p>完成度: ${passed}/${total} (${percentage}%)</p>
                    <p>测试时间: ${new Date().toLocaleString()}</p>
                    <p>状态: ${percentage >= 100 ? '✅ 全部通过' : percentage >= 70 ? '⚠️ 部分通过' : '❌ 需要修复'}</p>
                </div>
            `;
        }

        // 页面加载时初始化
        window.onload = function() {
            checkToken();
            checkUrl();
        };

        // 监听 storage 变化
        window.addEventListener('storage', function(e) {
            if (e.key === 'authToken') {
                checkToken();
            }
        });
    </script>
</body>
</html>
