# 前端认证流程验证说明文档

## 📋 验证目标

验证以下功能是否正常工作：
1. ✅ 路由守卫自动重定向未登录用户到登录页
2. ✅ 登录功能和 Token 存储
3. ✅ 已登录用户的页面访问权限
4. ✅ 页面刷新后的认证状态保持
5. ✅ 退出登录功能

## 🚀 启动步骤

### 步骤 1: 启动前端开发服务器

```bash
# 进入前端项目目录
cd applications/web-console

# 安装依赖（如果还没有安装）
npm install

# 启动开发服务器
npm run dev
```

**预期输出**:
```
  VITE v7.0.4  ready in 1234 ms

  ➜  Local:   http://localhost:5173/
  ➜  Network: use --host to expose
  ➜  press h + enter to show help
```

### 步骤 2: 建立 API 网关端口转发（可选）

在新终端中运行：
```bash
# 检查 API 网关服务名称
kubectl get svc | grep gateway

# 建立端口转发
kubectl port-forward svc/api-gateway-svc 8080:8080
```

**注意**: 如果没有 API 网关或者想要纯前端测试，可以跳过此步骤。

### 步骤 3: 打开浏览器

访问: `http://localhost:5173`

## 🧪 验证测试用例

### 测试用例 1: 未登录用户自动重定向

**操作步骤**:
1. 打开浏览器开发者工具 (F12)
2. 清除 localStorage: 在控制台执行 `localStorage.clear()`
3. 访问 `http://localhost:5173/`

**期望结果**:
- ✅ 自动重定向到 `/login` 页面
- ✅ 地址栏显示: `http://localhost:5173/login`
- ✅ 控制台显示路由守卫日志: "未登录，重定向到登录页"

**验证截图位置**: 
- 地址栏显示 `/login`
- 页面显示登录表单

### 测试用例 2: 直接访问受保护页面

**操作步骤**:
1. 确保已清除 localStorage
2. 直接访问 `http://localhost:5173/dashboard`
3. 直接访问 `http://localhost:5173/tasks`

**期望结果**:
- ✅ 所有受保护页面都重定向到 `/login`
- ✅ 控制台显示: "路由守卫检查: requiresAuth: true, hasToken: false"

### 测试用例 3: 登录功能测试

**操作步骤**:
1. 在登录页面输入任意用户名和密码
   - 用户名: `admin`
   - 密码: `123456`
2. 点击"登录"按钮
3. 观察页面变化和控制台输出

**期望结果**:
- ✅ 显示"登录成功"的成功提示消息
- ✅ 自动跳转到 `/dashboard` 页面
- ✅ localStorage 中存储了 `authToken`
- ✅ 页面显示完整的后台布局（侧边栏 + 主内容区）

**验证方法**:
```javascript
// 在浏览器控制台检查 Token
console.log(localStorage.getItem('authToken'))
// 应该输出一个 token 字符串
```

### 测试用例 4: 已登录用户访问登录页

**操作步骤**:
1. 确保已登录（localStorage 中有 authToken）
2. 手动访问 `http://localhost:5173/login`

**期望结果**:
- ✅ 自动重定向到 `/dashboard` 页面
- ✅ 控制台显示: "已登录，重定向到首页"

### 测试用例 5: 页面刷新后认证状态保持

**操作步骤**:
1. 确保已登录并在 `/dashboard` 页面
2. 按 F5 刷新页面
3. 或者按 Ctrl+R 刷新页面

**期望结果**:
- ✅ 页面刷新后仍然显示 `/dashboard`
- ✅ 不会重定向到登录页
- ✅ 侧边栏菜单正常显示
- ✅ 用户信息正常显示

### 测试用例 6: 导航菜单功能

**操作步骤**:
1. 确保已登录
2. 点击侧边栏的各个菜单项：
   - 仪表板
   - 任务管理
   - 模拟执行
   - 分析报告

**期望结果**:
- ✅ 每个菜单项点击后正确跳转到对应页面
- ✅ 地址栏 URL 正确变化
- ✅ 面包屑导航正确更新
- ✅ 当前菜单项高亮显示

### 测试用例 7: 侧边栏折叠功能

**操作步骤**:
1. 点击头部的折叠按钮（汉堡菜单图标）
2. 观察侧边栏变化

**期望结果**:
- ✅ 侧边栏从 220px 收缩到 80px
- ✅ Logo 和菜单文字隐藏，只显示图标
- ✅ 折叠按钮图标从 "折叠" 变为 "展开"

### 测试用例 8: 退出登录功能

**操作步骤**:
1. 点击右上角的用户头像
2. 在下拉菜单中点击"退出登录"

**期望结果**:
- ✅ 显示"退出登录成功"提示消息
- ✅ 自动跳转到 `/login` 页面
- ✅ localStorage 中的 `authToken` 被清除
- ✅ 再次访问受保护页面会重定向到登录页

**验证方法**:
```javascript
// 检查 Token 是否被清除
console.log(localStorage.getItem('authToken'))
// 应该输出 null
```

## 🔍 调试和故障排除

### 常见问题 1: 页面空白或组件未加载

**可能原因**:
- 组件导入路径错误
- Ant Design Vue 未正确安装

**解决方法**:
```bash
# 重新安装依赖
npm install

# 检查控制台错误信息
# 查看 Network 标签页是否有 404 错误
```

### 常见问题 2: 路由守卫不生效

**可能原因**:
- localStorage key 不匹配
- 路由配置错误

**调试方法**:
```javascript
// 检查路由守卫日志
// 在浏览器控制台查看 "路由守卫检查" 相关日志

// 手动检查 Token
localStorage.getItem('authToken')

// 手动设置 Token 进行测试
localStorage.setItem('authToken', 'test-token')
```

### 常见问题 3: API 调用失败

**可能原因**:
- API 网关未启动
- 端口转发未建立
- CORS 问题

**解决方法**:
1. 检查 API 网关状态:
```bash
kubectl get pods | grep gateway
kubectl logs -f <gateway-pod-name>
```

2. 检查端口转发:
```bash
kubectl port-forward svc/api-gateway-svc 8080:8080
```

3. 如果只测试前端，可以暂时跳过 API 调用

### 常见问题 4: 样式显示异常

**可能原因**:
- Ant Design Vue CSS 未正确导入
- 自定义样式冲突

**解决方法**:
```bash
# 检查 main.ts 中是否导入了 CSS
import 'ant-design-vue/dist/reset.css'
```

## 📊 验证检查清单

### 基础功能 ✅
- [ ] 前端开发服务器正常启动
- [ ] 页面能正常访问
- [ ] 控制台无严重错误

### 路由守卫 ✅
- [ ] 未登录用户自动重定向到 `/login`
- [ ] 已登录用户访问 `/login` 重定向到 `/dashboard`
- [ ] 页面刷新后认证状态保持

### 登录功能 ✅
- [ ] 登录表单正常显示
- [ ] 登录成功后跳转到 `/dashboard`
- [ ] Token 正确存储到 localStorage

### 布局功能 ✅
- [ ] 侧边栏菜单正常显示
- [ ] 菜单点击正确跳转
- [ ] 侧边栏折叠功能正常
- [ ] 面包屑导航正确更新

### 退出功能 ✅
- [ ] 退出登录清除 Token
- [ ] 退出后重定向到登录页
- [ ] 退出后无法访问受保护页面

## 🎯 成功标准

当所有测试用例都通过时，说明前端认证流程配置正确，可以进行下一步的开发工作。

## 📝 测试报告模板

```
测试时间: ___________
测试人员: ___________

基础功能:
- 前端服务启动: [ ] 通过 [ ] 失败
- 页面访问: [ ] 通过 [ ] 失败

路由守卫:
- 未登录重定向: [ ] 通过 [ ] 失败
- 已登录重定向: [ ] 通过 [ ] 失败
- 刷新保持状态: [ ] 通过 [ ] 失败

登录功能:
- 登录成功: [ ] 通过 [ ] 失败
- Token 存储: [ ] 通过 [ ] 失败

布局功能:
- 菜单导航: [ ] 通过 [ ] 失败
- 侧边栏折叠: [ ] 通过 [ ] 失败

退出功能:
- 退出登录: [ ] 通过 [ ] 失败

问题记录:
___________________________
___________________________
```
