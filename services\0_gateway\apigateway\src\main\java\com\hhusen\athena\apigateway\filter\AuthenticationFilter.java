package com.hhusen.athena.apigateway.filter;

import com.hhusen.athena.apigateway.util.JwtUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

@Component
public class AuthenticationFilter implements GlobalFilter, Ordered {

    private final JwtUtil jwtUtil;
    
    @Autowired
    public AuthenticationFilter(JwtUtil jwtUtil) {
        this.jwtUtil = jwtUtil;
    }

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        String path = request.getPath().toString();
        
        // 登录接口直接放行
        if (path.contains("/auth/login")) {
            return chain.filter(exchange);
        }
        
        // 获取Authorization请求头
        String authHeader = request.getHeaders().getFirst(HttpHeaders.AUTHORIZATION);
        
        // 检查请求头是否存在且格式正确
        if (authHeader == null || !authHeader.startsWith("Bearer ")) {
            return onError(exchange, "未提供授权令牌", HttpStatus.UNAUTHORIZED);
        }
        
        // 提取Token
        String token = authHeader.substring(7);
        
        // 验证Token
        try {
            if (!jwtUtil.validateToken(token)) {
                return onError(exchange, "无效的令牌", HttpStatus.UNAUTHORIZED);
            }
            
            // Token有效，从Token中提取用户名
            String username = jwtUtil.getUsernameFromToken(token);
            
            // 将用户名添加到请求头，传递给下游服务
            ServerHttpRequest modifiedRequest = request.mutate()
                .header("X-Auth-Username", username)
                .build();
            
            // 使用修改后的请求继续
            return chain.filter(exchange.mutate().request(modifiedRequest).build());
        } catch (Exception e) {
            return onError(exchange, "令牌处理出错: " + e.getMessage(), HttpStatus.UNAUTHORIZED);
        }
    }

    @Override
    public int getOrder() {
        // 设置过滤器执行顺序，值越小优先级越高
        return -1;
    }
    
    private Mono<Void> onError(ServerWebExchange exchange, String message, HttpStatus status) {
        ServerHttpResponse response = exchange.getResponse();
        response.setStatusCode(status);
        return response.setComplete();
    }
} 