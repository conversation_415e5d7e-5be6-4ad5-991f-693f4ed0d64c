server:
  port: 8080

spring:
  application:
    name: api-gateway
  cloud:
    gateway:
      routes:
        - id: user_service_route
          uri: lb://user-service-svc
          predicates:
            - Path=/auth/**
        - id: task_service_route
          uri: lb://task-service-svc
          predicates:
            - Path=/api/tasks/**
          filters:
            - StripPrefix=1
    kubernetes:
      discovery:
        enabled: true 