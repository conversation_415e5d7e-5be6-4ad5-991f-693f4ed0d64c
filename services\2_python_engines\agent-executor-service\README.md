# Agent Executor Service

Agent Executor Service 是 Athena 平台的智能代理执行服务，提供了与平台内部服务进行异步通信的工具函数，专为 LLM Function Calling 功能设计。

## 🛠️ 工具函数

### 1. `run_simulation(city_name, rainfall, temperature, population)`

**功能**: 创建新的水资源模拟任务

**参数**:
- `city_name` (str): 城市名称
- `rainfall` (float): 降雨量参数
- `temperature` (float): 温度参数  
- `population` (int): 人口数量参数

**返回**: 包含任务ID等信息的字典

**示例**:
```python
result = await run_simulation(
    city_name="北京",
    rainfall=50.5,
    temperature=25.0,
    population=2000000
)
# 返回: {"id": 123, "name": "北京水资源模拟", "status": "PENDING", ...}
```

### 2. `get_task_status(task_id)`

**功能**: 获取指定任务的状态信息

**参数**:
- `task_id` (int): 任务ID

**返回**: 包含任务详细信息的字典

**示例**:
```python
result = await get_task_status(task_id=123)
# 返回: {"id": 123, "status": "COMPLETED", "waterLevel": 85.5, ...}
```

### 3. `get_simulation_report(task_id, user_question=None)`

**功能**: 获取模拟任务的AI分析报告

**参数**:
- `task_id` (int): 任务ID
- `user_question` (str, optional): 用户的具体问题

**返回**: 完整的分析报告文本

**示例**:
```python
# 全面分析
report = await get_simulation_report(task_id=123)

# 针对特定问题的分析
report = await get_simulation_report(
    task_id=123, 
    user_question="为什么最终水位这么低？"
)
```

## 🧪 测试端点

服务提供了测试端点来验证工具函数的功能：

### POST `/test-tools/run-simulation`
```json
{
    "city_name": "上海",
    "rainfall": 45.0,
    "temperature": 28.0,
    "population": 2500000
}
```

### POST `/test-tools/get-task-status`
```json
{
    "task_id": 123
}
```

### POST `/test-tools/get-simulation-report`
```json
{
    "task_id": 123,
    "user_question": "分析一下水位变化的原因"
}
```

## 🔧 服务配置

### 环境变量
- `TASK_SERVICE_URL`: 任务服务地址 (默认: http://task-service-svc:80)
- `LLM_SERVICE_URL`: LLM服务地址 (默认: http://llm-service:80)
- `OPENAI_API_KEY`: OpenAI API密钥 (可选)

### 服务依赖
- **task-service**: 提供任务管理功能
- **llm-service**: 提供AI分析能力
- **PostgreSQL**: 任务数据存储
- **MLflow**: 实验跟踪
- **Neo4j**: 知识图谱

## 🚀 Function Calling 集成

这些工具函数专为 OpenAI Function Calling 设计，可以直接用于构建智能代理：

```python
tools = [
    {
        "type": "function",
        "function": {
            "name": "run_simulation",
            "description": "创建新的水资源模拟任务",
            "parameters": {
                "type": "object",
                "properties": {
                    "city_name": {"type": "string", "description": "城市名称"},
                    "rainfall": {"type": "number", "description": "降雨量参数"},
                    "temperature": {"type": "number", "description": "温度参数"},
                    "population": {"type": "integer", "description": "人口数量参数"}
                },
                "required": ["city_name", "rainfall", "temperature", "population"]
            }
        }
    }
    # ... 其他工具定义
]
```

## 📊 健康检查

- **GET `/health`**: 服务健康状态检查
- **GET `/`**: 服务信息和可用端点列表

## 🔍 日志记录

服务提供详细的日志记录，包括：
- 工具函数调用日志
- 服务间通信日志
- 错误和异常日志
- 性能监控日志

所有日志都使用结构化格式，便于监控和调试。
