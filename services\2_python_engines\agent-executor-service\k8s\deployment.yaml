apiVersion: apps/v1
kind: Deployment
metadata:
  name: agent-executor-service
  labels:
    app: agent-executor-service
spec:
  replicas: 1
  selector:
    matchLabels:
      app: agent-executor-service
  template:
    metadata:
      labels:
        app: agent-executor-service
    spec:
      containers:
      - name: agent-executor-service
        image: athena/agent-executor-service:0.0.1
        ports:
        - containerPort: 8000
        env:
        # LLM API 配置 - 支持 OpenAI 和通义千问
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: llm-api-secret
              key: OPENAI_API_KEY
              optional: true
        - name: DASHSCOPE_API_KEY
          valueFrom:
            secretKeyRef:
              name: llm-api-secret
              key: DASHSCOPE_API_KEY
        - name: QWEN_BASE_URL
          value: "https://dashscope.aliyuncs.com/compatible-mode/v1"
        # 内部服务 URL 配置
        - name: TASK_SERVICE_URL
          value: "http://task-service-svc:80"
        - name: LLM_SERVICE_URL
          value: "http://llm-service:80"
        - name: KN<PERSON>LEDGE_SERVICE_URL
          value: "http://knowledge-service-svc:80"
        - name: MLFLOW_TRACKING_URI
          value: "http://mlflow-svc:5000"
        resources:
          requests:
            memory: "128Mi"
            cpu: "50m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
---
apiVersion: v1
kind: Service
metadata:
  name: agent-executor-service
  labels:
    app: agent-executor-service
spec:
  type: NodePort
  selector:
    app: agent-executor-service
  ports:
  - port: 80
    targetPort: 8000
    protocol: TCP
    name: http
