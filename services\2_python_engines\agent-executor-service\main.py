import os
import logging
import asyncio
from fastapi import FastAPI, HTTPException
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
from typing import Optional, List, Dict, Any, Union
import httpx
import requests
import json
from openai import OpenAI, AsyncOpenAI

# Configure logging
logging.basicConfig(
    level=logging.INFO, 
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# Create custom logger
logger = logging.getLogger('agent-executor-service')
logger.setLevel(logging.DEBUG)

# Environment variables for configuration
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY", "")
TASK_SERVICE_URL = os.getenv("TASK_SERVICE_URL", "http://task-service-svc:80")
LLM_SERVICE_URL = os.getenv("LLM_SERVICE_URL", "http://llm-service:80")

# Initialize OpenAI client
if OPENAI_API_KEY:
    openai_client = AsyncOpenAI(api_key=OPENAI_API_KEY)
    logger.info("OpenAI client initialized successfully")
else:
    openai_client = None
    logger.warning("OpenAI API key not provided, Function Calling will not be available")

# ============================================================================
# 工具函数 - 用于与平台内部服务进行异步通信
# ============================================================================

# OpenAI Function Calling 工具定义
TOOLS_SCHEMA = [
    {
        "type": "function",
        "function": {
            "name": "run_simulation",
            "description": "创建新的水资源模拟任务。当用户想要运行模拟、创建新任务或开始水资源分析时使用此工具。",
            "parameters": {
                "type": "object",
                "properties": {
                    "city_name": {
                        "type": "string",
                        "description": "要模拟的城市名称，例如：北京、上海、深圳等"
                    },
                    "rainfall": {
                        "type": "number",
                        "description": "降雨量参数（毫米），影响水资源供给，通常范围在0-200之间"
                    },
                    "temperature": {
                        "type": "number",
                        "description": "温度参数（摄氏度），影响蒸发率和用水需求，通常范围在-10到45之间"
                    },
                    "population": {
                        "type": "integer",
                        "description": "城市人口数量，影响用水需求，例如：1000000表示100万人口"
                    }
                },
                "required": ["city_name", "rainfall", "temperature", "population"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "get_task_status",
            "description": "获取指定任务的当前状态和详细信息。当用户询问任务进度、状态或想查看任务详情时使用此工具。",
            "parameters": {
                "type": "object",
                "properties": {
                    "task_id": {
                        "type": "integer",
                        "description": "要查询的任务ID，通常是之前创建任务时返回的数字ID"
                    }
                },
                "required": ["task_id"]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "get_simulation_report",
            "description": "获取模拟任务的AI分析报告。当用户想要了解模拟结果、分析数据或询问特定问题时使用此工具。",
            "parameters": {
                "type": "object",
                "properties": {
                    "task_id": {
                        "type": "integer",
                        "description": "要分析的任务ID"
                    },
                    "user_question": {
                        "type": "string",
                        "description": "用户的具体问题或关注点，如果用户没有特定问题则可以省略此参数进行全面分析"
                    }
                },
                "required": ["task_id"]
            }
        }
    }
]

async def run_simulation(city_name: str, rainfall: float, temperature: float, population: int) -> dict:
    """
    调用 task-service 的 POST /tasks 接口创建新的模拟任务

    Args:
        city_name (str): 城市名称
        rainfall (float): 降雨量参数
        temperature (float): 温度参数
        population (int): 人口数量参数

    Returns:
        dict: 创建的任务信息，包含 taskId 等字段

    Raises:
        HTTPException: 当服务调用失败时抛出异常
    """
    try:
        logger.info(f"正在创建模拟任务 - 城市: {city_name}, 降雨量: {rainfall}, 温度: {temperature}, 人口: {population}")

        # 构造请求体，参考 Task.java 实体类的字段
        request_body = {
            "name": f"{city_name}水资源模拟",
            "cityName": city_name,
            "rainfall": rainfall,
            "temperature": temperature,
            "population": population,
            "status": "PENDING",
            "initialWaterLevel": 100.0  # 默认初始水位
        }

        # 使用 httpx 发送异步 POST 请求
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.post(
                f"{TASK_SERVICE_URL}/tasks",
                json=request_body,
                headers={"Content-Type": "application/json"}
            )
            response.raise_for_status()

            task_data = response.json()
            logger.info(f"成功创建任务，任务ID: {task_data.get('id')}")
            return task_data

    except httpx.HTTPStatusError as e:
        logger.error(f"创建模拟任务时发生HTTP错误: {e.response.status_code} - {e.response.text}")
        raise HTTPException(
            status_code=e.response.status_code,
            detail=f"任务服务错误: {e.response.text}"
        )
    except httpx.RequestError as e:
        logger.error(f"无法连接到任务服务: {e}")
        raise HTTPException(
            status_code=503,
            detail=f"无法连接到任务服务: {str(e)}"
        )
    except Exception as e:
        logger.error(f"创建模拟任务时发生未知错误: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"内部服务器错误: {str(e)}"
        )

async def get_task_status(task_id: int) -> dict:
    """
    调用 task-service 的 GET /tasks/{task_id} 接口获取任务状态

    Args:
        task_id (int): 任务ID

    Returns:
        dict: 任务的详细信息，包含 status 字段等

    Raises:
        HTTPException: 当服务调用失败时抛出异常
    """
    try:
        logger.info(f"正在获取任务状态，任务ID: {task_id}")

        # 使用 httpx 发送异步 GET 请求
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.get(f"{TASK_SERVICE_URL}/tasks/{task_id}")
            response.raise_for_status()

            task_data = response.json()
            logger.info(f"成功获取任务状态，任务ID: {task_id}, 状态: {task_data.get('status')}")
            return task_data

    except httpx.HTTPStatusError as e:
        if e.response.status_code == 404:
            logger.warning(f"任务不存在，任务ID: {task_id}")
            raise HTTPException(
                status_code=404,
                detail=f"任务ID {task_id} 不存在"
            )
        else:
            logger.error(f"获取任务状态时发生HTTP错误: {e.response.status_code} - {e.response.text}")
            raise HTTPException(
                status_code=e.response.status_code,
                detail=f"任务服务错误: {e.response.text}"
            )
    except httpx.RequestError as e:
        logger.error(f"无法连接到任务服务: {e}")
        raise HTTPException(
            status_code=503,
            detail=f"无法连接到任务服务: {str(e)}"
        )
    except Exception as e:
        logger.error(f"获取任务状态时发生未知错误: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"内部服务器错误: {str(e)}"
        )

async def get_simulation_report(task_id: int, user_question: str = None) -> str:
    """
    调用 llm-service 的 POST /explain-stream 接口获取模拟分析报告

    Args:
        task_id (int): 任务ID
        user_question (str, optional): 用户的具体问题，如果为None则进行全面分析

    Returns:
        str: 完整的分析报告文本

    Raises:
        HTTPException: 当服务调用失败时抛出异常
    """
    try:
        logger.info(f"正在获取模拟分析报告，任务ID: {task_id}, 用户问题: {user_question}")

        # 构造请求体，参考 llm-service 的 ExplainRequest 模型
        request_body = {
            "task_id": task_id
        }
        if user_question and user_question.strip():
            request_body["user_question"] = user_question.strip()

        # 使用 httpx 发送异步 POST 请求处理流式响应
        async with httpx.AsyncClient(timeout=120.0) as client:  # 增加超时时间，因为LLM响应可能较慢
            async with client.stream(
                "POST",
                f"{LLM_SERVICE_URL}/explain-stream",
                json=request_body,
                headers={"Content-Type": "application/json"}
            ) as response:
                response.raise_for_status()

                # 收集所有流式响应块
                full_response = ""
                async for chunk in response.aiter_text():
                    if chunk:
                        full_response += chunk

                logger.info(f"成功获取模拟分析报告，任务ID: {task_id}, 报告长度: {len(full_response)} 字符")
                return full_response

    except httpx.HTTPStatusError as e:
        if e.response.status_code == 404:
            logger.warning(f"任务不存在或LLM服务不可用，任务ID: {task_id}")
            raise HTTPException(
                status_code=404,
                detail=f"任务ID {task_id} 不存在或LLM服务不可用"
            )
        else:
            logger.error(f"获取分析报告时发生HTTP错误: {e.response.status_code} - {e.response.text}")
            raise HTTPException(
                status_code=e.response.status_code,
                detail=f"LLM服务错误: {e.response.text}"
            )
    except httpx.RequestError as e:
        logger.error(f"无法连接到LLM服务: {e}")
        raise HTTPException(
            status_code=503,
            detail=f"无法连接到LLM服务: {str(e)}"
        )
    except Exception as e:
        logger.error(f"获取分析报告时发生未知错误: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"内部服务器错误: {str(e)}"
        )

# ============================================================================
# Agent 循环核心逻辑
# ============================================================================

async def agent_loop_generator(user_command: str):
    """
    Agent 循环生成器，实现完整的思考-决策-行动-观察循环

    Args:
        user_command (str): 用户的自然语言命令

    Yields:
        str: 流式响应内容
    """
    try:
        # 初始化消息列表
        messages = [
            {
                "role": "system",
                "content": """你是雅典娜(Athena)平台的智能代理助手。你可以帮助用户进行水资源模拟分析。

你有以下工具可以使用：
1. run_simulation - 创建新的水资源模拟任务
2. get_task_status - 获取任务状态和详细信息
3. get_simulation_report - 获取模拟任务的AI分析报告

请根据用户的需求，合理使用这些工具来完成任务。如果需要创建模拟，请询问用户提供必要的参数（城市名称、降雨量、温度、人口）。

始终以友好、专业的方式与用户交互，并提供清晰的解释和建议。"""
            },
            {
                "role": "user",
                "content": user_command
            }
        ]

        max_iterations = 5  # 最大循环次数
        iteration = 0

        yield f"🤖 正在处理您的请求: {user_command}\n\n"

        while iteration < max_iterations:
            iteration += 1
            logger.info(f"Agent循环第 {iteration} 轮开始")

            yield f"💭 **第 {iteration} 轮思考中...**\n"

            # 思考阶段：调用 LLM
            try:
                response = await openai_client.chat.completions.create(
                    model="gpt-4-turbo-preview",  # 或使用其他支持Function Calling的模型
                    messages=messages,
                    tools=TOOLS_SCHEMA,
                    tool_choice="auto",
                    temperature=0.7
                )

                assistant_message = response.choices[0].message
                logger.info(f"LLM响应: {assistant_message}")

            except Exception as e:
                logger.error(f"调用OpenAI API失败: {e}")
                yield f"❌ 调用AI服务失败: {str(e)}\n"
                return

            # 将助手消息添加到对话历史
            messages.append({
                "role": "assistant",
                "content": assistant_message.content,
                "tool_calls": assistant_message.tool_calls
            })

            # 决策阶段：检查是否需要调用工具
            if not assistant_message.tool_calls:
                # 没有工具调用，任务完成
                yield f"✅ **任务完成！**\n\n"
                if assistant_message.content:
                    yield f"{assistant_message.content}\n"
                logger.info("Agent循环完成，无需调用工具")
                return

            # 行动阶段：执行工具调用
            yield f"🔧 **正在执行 {len(assistant_message.tool_calls)} 个工具调用...**\n"

            for tool_call in assistant_message.tool_calls:
                function_name = tool_call.function.name
                function_args = json.loads(tool_call.function.arguments)

                yield f"   ⚡ 调用工具: {function_name}\n"
                logger.info(f"调用工具: {function_name}, 参数: {function_args}")

                # 执行对应的工具函数
                try:
                    if function_name == "run_simulation":
                        result = await run_simulation(
                            city_name=function_args["city_name"],
                            rainfall=function_args["rainfall"],
                            temperature=function_args["temperature"],
                            population=function_args["population"]
                        )
                    elif function_name == "get_task_status":
                        result = await get_task_status(
                            task_id=function_args["task_id"]
                        )
                    elif function_name == "get_simulation_report":
                        result = await get_simulation_report(
                            task_id=function_args["task_id"],
                            user_question=function_args.get("user_question")
                        )
                    else:
                        result = {"error": f"未知的工具函数: {function_name}"}

                    yield f"   ✅ 工具执行成功\n"
                    logger.info(f"工具 {function_name} 执行成功")

                except Exception as e:
                    result = {"error": f"工具执行失败: {str(e)}"}
                    yield f"   ❌ 工具执行失败: {str(e)}\n"
                    logger.error(f"工具 {function_name} 执行失败: {e}")

                # 观察阶段：将工具结果添加到消息历史
                messages.append({
                    "role": "tool",
                    "tool_call_id": tool_call.id,
                    "content": json.dumps(result, ensure_ascii=False)
                })

            yield f"\n"

        # 达到最大迭代次数
        yield f"⚠️ 已达到最大处理轮数 ({max_iterations})，任务可能未完全完成。\n"
        logger.warning(f"Agent循环达到最大迭代次数: {max_iterations}")

    except Exception as e:
        logger.error(f"Agent循环发生错误: {e}", exc_info=True)
        yield f"❌ 处理过程中发生错误: {str(e)}\n"

# FastAPI application instance
app = FastAPI(
    title="Agent Executor Service",
    description="A microservice for executing agent commands in the Athena platform",
    version="1.0.0"
)

class ExecuteCommandRequest(BaseModel):
    command: str

class ExecuteCommandResponse(BaseModel):
    status: str
    message: str
    command: str

class HealthResponse(BaseModel):
    status: str
    service: str
    version: str

@app.get("/health", response_model=HealthResponse)
async def health_check():
    """
    Health check endpoint to verify service status
    """
    try:
        logger.info("Health check requested")
        return HealthResponse(
            status="healthy",
            service="agent-executor-service",
            version="1.0.0"
        )
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=500, detail="Service unhealthy")

@app.post("/execute-command")
async def execute_command(request: ExecuteCommandRequest):
    """
    执行智能代理命令，支持完整的 Agent 循环和流式响应

    Args:
        request: ExecuteCommandRequest containing the command to execute

    Returns:
        StreamingResponse with agent execution results
    """
    try:
        logger.info(f"Received agent command: {request.command}")

        # Validate command
        if not request.command or not request.command.strip():
            logger.warning("Empty command received")
            raise HTTPException(
                status_code=400,
                detail="Command cannot be empty"
            )

        # Check if OpenAI client is available
        if not openai_client:
            logger.error("OpenAI client not initialized")
            raise HTTPException(
                status_code=503,
                detail="OpenAI API key not configured, Function Calling not available"
            )

        # Return streaming response
        return StreamingResponse(
            agent_loop_generator(request.command.strip()),
            media_type="text/event-stream"
        )

    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Unexpected error during agent execution: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )

@app.get("/")
async def root():
    """
    Root endpoint providing service information
    """
    return {
        "service": "agent-executor-service",
        "version": "2.0.0",
        "description": "智能代理执行服务 - 支持 OpenAI Function Calling 和完整的 Agent 循环",
        "features": {
            "agent_loop": "完整的思考-决策-行动-观察循环",
            "streaming_response": "流式响应支持",
            "function_calling": "OpenAI Function Calling 集成",
            "tool_execution": "异步工具执行"
        },
        "endpoints": {
            "health": "/health - 健康检查",
            "execute_command": "/execute-command - 智能代理命令执行（流式响应）",
            "test_tools": "/test-tools/* - 工具函数测试端点"
        },
        "tools": {
            "run_simulation": "创建新的水资源模拟任务",
            "get_task_status": "获取任务状态和详细信息",
            "get_simulation_report": "获取模拟任务的AI分析报告"
        },
        "openai_configured": openai_client is not None
    }

@app.get("/tools-schema")
async def get_tools_schema():
    """
    获取 OpenAI Function Calling 工具的 JSON Schema 定义
    """
    return {
        "description": "OpenAI Function Calling 工具定义",
        "tools": TOOLS_SCHEMA
    }

# ============================================================================
# 测试端点 - 用于验证工具函数功能
# ============================================================================

class SimulationRequest(BaseModel):
    city_name: str
    rainfall: float
    temperature: float
    population: int

class TaskStatusRequest(BaseModel):
    task_id: int

class ReportRequest(BaseModel):
    task_id: int
    user_question: Optional[str] = None

@app.post("/test-tools/run-simulation")
async def test_run_simulation(request: SimulationRequest):
    """
    测试 run_simulation 工具函数
    """
    try:
        result = await run_simulation(
            city_name=request.city_name,
            rainfall=request.rainfall,
            temperature=request.temperature,
            population=request.population
        )
        return {
            "status": "success",
            "message": "模拟任务创建成功",
            "data": result
        }
    except Exception as e:
        logger.error(f"测试 run_simulation 失败: {e}")
        raise

@app.post("/test-tools/get-task-status")
async def test_get_task_status(request: TaskStatusRequest):
    """
    测试 get_task_status 工具函数
    """
    try:
        result = await get_task_status(task_id=request.task_id)
        return {
            "status": "success",
            "message": "任务状态获取成功",
            "data": result
        }
    except Exception as e:
        logger.error(f"测试 get_task_status 失败: {e}")
        raise

@app.post("/test-tools/get-simulation-report")
async def test_get_simulation_report(request: ReportRequest):
    """
    测试 get_simulation_report 工具函数
    """
    try:
        result = await get_simulation_report(
            task_id=request.task_id,
            user_question=request.user_question
        )
        return {
            "status": "success",
            "message": "分析报告获取成功",
            "data": result
        }
    except Exception as e:
        logger.error(f"测试 get_simulation_report 失败: {e}")
        raise

if __name__ == "__main__":
    import uvicorn
    
    logger.info("Starting Agent Executor Service...")
    uvicorn.run(app, host="0.0.0.0", port=8000)
