import os
import logging
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from typing import Optional
import httpx
import requests
import json

# Configure logging
logging.basicConfig(
    level=logging.INFO, 
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# Create custom logger
logger = logging.getLogger('agent-executor-service')
logger.setLevel(logging.DEBUG)

# Environment variables for configuration
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY", "")
TASK_SERVICE_URL = os.getenv("TASK_SERVICE_URL", "http://task-service-svc:80")
LLM_SERVICE_URL = os.getenv("LLM_SERVICE_URL", "http://llm-service:80")

# ============================================================================
# 工具函数 - 用于与平台内部服务进行异步通信
# ============================================================================

async def run_simulation(city_name: str, rainfall: float, temperature: float, population: int) -> dict:
    """
    调用 task-service 的 POST /tasks 接口创建新的模拟任务

    Args:
        city_name (str): 城市名称
        rainfall (float): 降雨量参数
        temperature (float): 温度参数
        population (int): 人口数量参数

    Returns:
        dict: 创建的任务信息，包含 taskId 等字段

    Raises:
        HTTPException: 当服务调用失败时抛出异常
    """
    try:
        logger.info(f"正在创建模拟任务 - 城市: {city_name}, 降雨量: {rainfall}, 温度: {temperature}, 人口: {population}")

        # 构造请求体，参考 Task.java 实体类的字段
        request_body = {
            "name": f"{city_name}水资源模拟",
            "cityName": city_name,
            "rainfall": rainfall,
            "temperature": temperature,
            "population": population,
            "status": "PENDING",
            "initialWaterLevel": 100.0  # 默认初始水位
        }

        # 使用 httpx 发送异步 POST 请求
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.post(
                f"{TASK_SERVICE_URL}/tasks",
                json=request_body,
                headers={"Content-Type": "application/json"}
            )
            response.raise_for_status()

            task_data = response.json()
            logger.info(f"成功创建任务，任务ID: {task_data.get('id')}")
            return task_data

    except httpx.HTTPStatusError as e:
        logger.error(f"创建模拟任务时发生HTTP错误: {e.response.status_code} - {e.response.text}")
        raise HTTPException(
            status_code=e.response.status_code,
            detail=f"任务服务错误: {e.response.text}"
        )
    except httpx.RequestError as e:
        logger.error(f"无法连接到任务服务: {e}")
        raise HTTPException(
            status_code=503,
            detail=f"无法连接到任务服务: {str(e)}"
        )
    except Exception as e:
        logger.error(f"创建模拟任务时发生未知错误: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"内部服务器错误: {str(e)}"
        )

async def get_task_status(task_id: int) -> dict:
    """
    调用 task-service 的 GET /tasks/{task_id} 接口获取任务状态

    Args:
        task_id (int): 任务ID

    Returns:
        dict: 任务的详细信息，包含 status 字段等

    Raises:
        HTTPException: 当服务调用失败时抛出异常
    """
    try:
        logger.info(f"正在获取任务状态，任务ID: {task_id}")

        # 使用 httpx 发送异步 GET 请求
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.get(f"{TASK_SERVICE_URL}/tasks/{task_id}")
            response.raise_for_status()

            task_data = response.json()
            logger.info(f"成功获取任务状态，任务ID: {task_id}, 状态: {task_data.get('status')}")
            return task_data

    except httpx.HTTPStatusError as e:
        if e.response.status_code == 404:
            logger.warning(f"任务不存在，任务ID: {task_id}")
            raise HTTPException(
                status_code=404,
                detail=f"任务ID {task_id} 不存在"
            )
        else:
            logger.error(f"获取任务状态时发生HTTP错误: {e.response.status_code} - {e.response.text}")
            raise HTTPException(
                status_code=e.response.status_code,
                detail=f"任务服务错误: {e.response.text}"
            )
    except httpx.RequestError as e:
        logger.error(f"无法连接到任务服务: {e}")
        raise HTTPException(
            status_code=503,
            detail=f"无法连接到任务服务: {str(e)}"
        )
    except Exception as e:
        logger.error(f"获取任务状态时发生未知错误: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"内部服务器错误: {str(e)}"
        )

async def get_simulation_report(task_id: int, user_question: str = None) -> str:
    """
    调用 llm-service 的 POST /explain-stream 接口获取模拟分析报告

    Args:
        task_id (int): 任务ID
        user_question (str, optional): 用户的具体问题，如果为None则进行全面分析

    Returns:
        str: 完整的分析报告文本

    Raises:
        HTTPException: 当服务调用失败时抛出异常
    """
    try:
        logger.info(f"正在获取模拟分析报告，任务ID: {task_id}, 用户问题: {user_question}")

        # 构造请求体，参考 llm-service 的 ExplainRequest 模型
        request_body = {
            "task_id": task_id
        }
        if user_question and user_question.strip():
            request_body["user_question"] = user_question.strip()

        # 使用 httpx 发送异步 POST 请求处理流式响应
        async with httpx.AsyncClient(timeout=120.0) as client:  # 增加超时时间，因为LLM响应可能较慢
            async with client.stream(
                "POST",
                f"{LLM_SERVICE_URL}/explain-stream",
                json=request_body,
                headers={"Content-Type": "application/json"}
            ) as response:
                response.raise_for_status()

                # 收集所有流式响应块
                full_response = ""
                async for chunk in response.aiter_text():
                    if chunk:
                        full_response += chunk

                logger.info(f"成功获取模拟分析报告，任务ID: {task_id}, 报告长度: {len(full_response)} 字符")
                return full_response

    except httpx.HTTPStatusError as e:
        if e.response.status_code == 404:
            logger.warning(f"任务不存在或LLM服务不可用，任务ID: {task_id}")
            raise HTTPException(
                status_code=404,
                detail=f"任务ID {task_id} 不存在或LLM服务不可用"
            )
        else:
            logger.error(f"获取分析报告时发生HTTP错误: {e.response.status_code} - {e.response.text}")
            raise HTTPException(
                status_code=e.response.status_code,
                detail=f"LLM服务错误: {e.response.text}"
            )
    except httpx.RequestError as e:
        logger.error(f"无法连接到LLM服务: {e}")
        raise HTTPException(
            status_code=503,
            detail=f"无法连接到LLM服务: {str(e)}"
        )
    except Exception as e:
        logger.error(f"获取分析报告时发生未知错误: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"内部服务器错误: {str(e)}"
        )

# FastAPI application instance
app = FastAPI(
    title="Agent Executor Service",
    description="A microservice for executing agent commands in the Athena platform",
    version="1.0.0"
)

class ExecuteCommandRequest(BaseModel):
    command: str

class ExecuteCommandResponse(BaseModel):
    status: str
    message: str
    command: str

class HealthResponse(BaseModel):
    status: str
    service: str
    version: str

@app.get("/health", response_model=HealthResponse)
async def health_check():
    """
    Health check endpoint to verify service status
    """
    try:
        logger.info("Health check requested")
        return HealthResponse(
            status="healthy",
            service="agent-executor-service",
            version="1.0.0"
        )
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=500, detail="Service unhealthy")

@app.post("/execute-command", response_model=ExecuteCommandResponse)
async def execute_command(request: ExecuteCommandRequest):
    """
    Execute a command through the agent executor
    
    Args:
        request: ExecuteCommandRequest containing the command to execute
        
    Returns:
        ExecuteCommandResponse with execution status and details
    """
    try:
        logger.info(f"Received command execution request: {request.command}")
        
        # Validate command
        if not request.command or not request.command.strip():
            logger.warning("Empty command received")
            raise HTTPException(
                status_code=400, 
                detail="Command cannot be empty"
            )
        
        # Process the command (placeholder implementation)
        # In a real implementation, this would integrate with agent logic
        command = request.command.strip()
        
        logger.info(f"Processing command: {command}")
        
        # Simulate command processing
        if command.lower().startswith("error"):
            logger.error(f"Command execution failed: {command}")
            raise HTTPException(
                status_code=400,
                detail=f"Command execution failed: {command}"
            )
        
        # Success response
        response = ExecuteCommandResponse(
            status="success",
            message=f"Command '{command}' executed successfully",
            command=command
        )
        
        logger.info(f"Command executed successfully: {command}")
        return response
        
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Unexpected error during command execution: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )

@app.get("/")
async def root():
    """
    Root endpoint providing service information
    """
    return {
        "service": "agent-executor-service",
        "version": "1.0.0",
        "description": "Agent Executor Service for Athena Platform",
        "endpoints": {
            "health": "/health",
            "execute_command": "/execute-command",
            "test_tools": "/test-tools"
        },
        "tools": {
            "run_simulation": "创建新的模拟任务",
            "get_task_status": "获取任务状态",
            "get_simulation_report": "获取模拟分析报告"
        }
    }

# ============================================================================
# 测试端点 - 用于验证工具函数功能
# ============================================================================

class SimulationRequest(BaseModel):
    city_name: str
    rainfall: float
    temperature: float
    population: int

class TaskStatusRequest(BaseModel):
    task_id: int

class ReportRequest(BaseModel):
    task_id: int
    user_question: Optional[str] = None

@app.post("/test-tools/run-simulation")
async def test_run_simulation(request: SimulationRequest):
    """
    测试 run_simulation 工具函数
    """
    try:
        result = await run_simulation(
            city_name=request.city_name,
            rainfall=request.rainfall,
            temperature=request.temperature,
            population=request.population
        )
        return {
            "status": "success",
            "message": "模拟任务创建成功",
            "data": result
        }
    except Exception as e:
        logger.error(f"测试 run_simulation 失败: {e}")
        raise

@app.post("/test-tools/get-task-status")
async def test_get_task_status(request: TaskStatusRequest):
    """
    测试 get_task_status 工具函数
    """
    try:
        result = await get_task_status(task_id=request.task_id)
        return {
            "status": "success",
            "message": "任务状态获取成功",
            "data": result
        }
    except Exception as e:
        logger.error(f"测试 get_task_status 失败: {e}")
        raise

@app.post("/test-tools/get-simulation-report")
async def test_get_simulation_report(request: ReportRequest):
    """
    测试 get_simulation_report 工具函数
    """
    try:
        result = await get_simulation_report(
            task_id=request.task_id,
            user_question=request.user_question
        )
        return {
            "status": "success",
            "message": "分析报告获取成功",
            "data": result
        }
    except Exception as e:
        logger.error(f"测试 get_simulation_report 失败: {e}")
        raise

if __name__ == "__main__":
    import uvicorn
    
    logger.info("Starting Agent Executor Service...")
    uvicorn.run(app, host="0.0.0.0", port=8000)
