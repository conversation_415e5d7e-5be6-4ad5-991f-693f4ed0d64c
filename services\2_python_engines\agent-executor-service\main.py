import os
import logging
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from typing import Optional
import httpx
import requests

# Configure logging
logging.basicConfig(
    level=logging.INFO, 
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# Create custom logger
logger = logging.getLogger('agent-executor-service')
logger.setLevel(logging.DEBUG)

# Environment variables for configuration
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY", "")
TASK_SERVICE_URL = os.getenv("TASK_SERVICE_URL", "http://task-service-svc:80")
LLM_SERVICE_URL = os.getenv("LLM_SERVICE_URL", "http://llm-service:80")

# FastAPI application instance
app = FastAPI(
    title="Agent Executor Service",
    description="A microservice for executing agent commands in the Athena platform",
    version="1.0.0"
)

class ExecuteCommandRequest(BaseModel):
    command: str

class ExecuteCommandResponse(BaseModel):
    status: str
    message: str
    command: str

class HealthResponse(BaseModel):
    status: str
    service: str
    version: str

@app.get("/health", response_model=HealthResponse)
async def health_check():
    """
    Health check endpoint to verify service status
    """
    try:
        logger.info("Health check requested")
        return HealthResponse(
            status="healthy",
            service="agent-executor-service",
            version="1.0.0"
        )
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=500, detail="Service unhealthy")

@app.post("/execute-command", response_model=ExecuteCommandResponse)
async def execute_command(request: ExecuteCommandRequest):
    """
    Execute a command through the agent executor
    
    Args:
        request: ExecuteCommandRequest containing the command to execute
        
    Returns:
        ExecuteCommandResponse with execution status and details
    """
    try:
        logger.info(f"Received command execution request: {request.command}")
        
        # Validate command
        if not request.command or not request.command.strip():
            logger.warning("Empty command received")
            raise HTTPException(
                status_code=400, 
                detail="Command cannot be empty"
            )
        
        # Process the command (placeholder implementation)
        # In a real implementation, this would integrate with agent logic
        command = request.command.strip()
        
        logger.info(f"Processing command: {command}")
        
        # Simulate command processing
        if command.lower().startswith("error"):
            logger.error(f"Command execution failed: {command}")
            raise HTTPException(
                status_code=400,
                detail=f"Command execution failed: {command}"
            )
        
        # Success response
        response = ExecuteCommandResponse(
            status="success",
            message=f"Command '{command}' executed successfully",
            command=command
        )
        
        logger.info(f"Command executed successfully: {command}")
        return response
        
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Unexpected error during command execution: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )

@app.get("/")
async def root():
    """
    Root endpoint providing service information
    """
    return {
        "service": "agent-executor-service",
        "version": "1.0.0",
        "description": "Agent Executor Service for Athena Platform",
        "endpoints": {
            "health": "/health",
            "execute_command": "/execute-command"
        }
    }

if __name__ == "__main__":
    import uvicorn
    
    logger.info("Starting Agent Executor Service...")
    uvicorn.run(app, host="0.0.0.0", port=8000)
