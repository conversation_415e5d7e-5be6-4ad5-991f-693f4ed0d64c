# Athena Core 开发记录

## v0.1.0: 项目奠基与首个服务本地运行

**发布日期**: 2025-07-04 16:08  
**核心目标**: 搭建项目骨架，并验证核心微服务能在本地成功运行。

### 主要变更 (Changelog)

#### [Init] 项目结构初始化
- 创建并初始化了 athena-core Monorepo Git 仓库
- 搭建了标准化的目录结构，包括 `services/`, `applications/`, `terraform/`, `docs/`, `scripts/`
- 添加了针对 Java Maven 项目的 `.gitignore` 文件

#### [Feat] task-service (任务服务) 创建
- 在 `services/1_java_backend/task-service/` 路径下，成功创建了第一个微服务——task-service
- 该服务基于 Spring Boot 3 和 Java 17
- 实现了一个极简的 RESTful API，包含两个端点：
  - `GET /`: 返回 "Hello from Athena Task Service!"，用于基本连通性测试
  - `GET /health`: 返回 "OK"，作为标准健康检查端点

#### [Docs] 本地开发环境验证
- 通过 `./mvnw spring-boot:run` 命令，成功在本地启动 task-service
- 通过浏览器访问 http://localhost:8080/ 和 http://localhost:8080/health，验证服务正常响应
- 确认了本地开发环境 (JDK, Maven) 的配置正确性

### 阶段成果
- 项目拥有了一个清晰、可扩展的 Monorepo 目录结构
- 第一个核心微服务 (task-service) 已经从概念阶段进入了可运行的代码阶段
- 为后续的容器化、CI/CD 集成奠定了坚实的基础

---

## v0.2.0: 服务容器化与跨平台构建

**发布日期**: 2025-07-04 22:57  
**核心目标**: 将 task-service 成功容器化，构建出标准的、可移植的 Docker 镜像，并解决在 Windows 环境下构建 Linux 容器的典型问题。

### 主要变更 (Changelog)

#### [Feat] task-service 容器化
- 在 `services/1_java_backend/task-service/` 目录下创建了 Dockerfile
- 采用了多阶段构建 (Multi-stage Build) 的最佳实践：
  - 第一阶段 (build stage) 使用 `maven:3.9.5-amazoncorretto-17` 镜像，负责编译和打包 Spring Boot 应用
  - 第二阶段 (final stage) 使用极小化的 `amazoncorretto:17-alpine-jre` 镜像作为最终运行环境，显著减小了生产镜像的体积 (约 201MB)
- 优化了 Docker 缓存机制，通过先 `COPY pom.xml` 再执行 `mvn dependency:go-offline` 的方式，大幅提升了后续构建的速度

#### [Fix] 解决 exec format error (CPU 架构问题)
- **问题描述**: 在 Windows (amd64) 环境下首次构建镜像后，运行容器时出现 exec format error 错误
- **诊断过程**: 
  - 初步判断为 CPU 架构不匹配。尽管 `docker image inspect` 显示架构为 amd64，但错误依旧
  - 通过 `--no-cache` 强制重新构建后发现，问题根源是 Docker 的构建缓存使用了之前可能为 arm64 平台构建的错误镜像层
- **解决方案**: 在构建命令中明确指定目标平台，并禁用缓存进行首次正确构建：
  ```bash
  docker buildx build --platform linux/amd64 --no-cache -t athena/task-service:0.0.10 . --load
  docker buildx build --platform linux/amd64 -t athena/pinn-engine:0.0.14 . --load
  docker buildx build --platform linux/amd64 -t athena/agent-engine:0.0.9 . --load
  docker buildx build --platform linux/amd64 -t athena/knowledge-service:0.0.8 . --load
  docker buildx build --platform linux/amd64 -t athena/api-gateway:0.0.2 . --load
  docker buildx build --platform linux/amd64 -t athena/user-service:0.0.2 . --load
  docker buildx build --platform linux/amd64 -t athena/llm-service:0.0.2 . --load
  ```

#### [Fix] 解决 exec format error (换行符问题)
- **问题描述**: 即使在解决了 CPU 架构和缓存问题后，exec format error 仍然出现
- **诊断过程**: 深入排查，定位到问题根源为 Windows (CRLF) 与 Linux (LF) 换行符不兼容
- **解决方案**: 在 Monorepo 根目录创建 `.gitattributes` 文件，强制所有文本文件使用 LF 换行符：
  ```
  * text=auto eol=lf
  ```

#### [Chore] Dockerfile 优化
- 整理并去除了 Dockerfile 中重复的 COPY 和 RUN 指令，使构建逻辑更清晰、高效
- 将基础镜像从 amazoncorretto 更换为 eclipse-temurin 进行排错尝试，最终确认问题与基础镜像无关

### 阶段成果
- **核心服务已实现标准容器化**: task-service 不再是只能在本地运行的应用，而是一个标准的、可在任何 Docker 环境中运行的容器
- **攻克了关键的跨平台构建难题**: 成功解决了在 Windows 上为 Linux 构建 Docker 镜像时的复杂问题
- **建立了高质量的 Dockerfile 规范**: 采用的多阶段构建和缓存优化策略，可作为后续所有微服务容器化的标准模板

---

## v0.3.0: 本地 Kubernetes 部署与服务暴露

**发布日期**: 2025-07-05 0:33  
**核心目标**: 成功搭建本地 Kubernetes (Minikube) 开发环境，并将容器化的 task-service 部署到集群中，通过网络稳定访问。

### 主要变更 (Changelog)

#### [Feat] 搭建本地 Kubernetes (Minikube) 环境
- **技术选型**: 在 Minikube 与 Kind 之间，选择 Minikube 作为本地 K8s 环境。理由是其功能更完备、对 Windows 兼容性更好

#### [Fix] 解决 Minikube kubectl 客户端下载失败问题
- **问题描述**: 集群成功启动后，执行 `minikube kubectl -- ...` 时，因无法从官方源下载与集群版本匹配的 Windows kubectl.exe 而导致 TLS handshake timeout
- **解决方案 (手动缓存法)**:
  - 从可访问的镜像站手动下载正确版本的 Windows kubectl.exe
  - 将其精确放置到 Windows 宿主机的缓存目录 `C:\Users\<USER>\.minikube\cache\windows\amd64\<version>\` 中
  ```bash
  docker pull registry.cn-hangzhou.aliyuncs.com/google_containers/kicbase:v0.0.42
  minikube start --force --base-image='registry.cn-hangzhou.aliyuncs.com/google_containers/kicbase:v0.0.42'
  ```
docker pull registry.cn-hangzhou.aliyuncs.com/google_containers/minio:latest
  
#### [Feat] task-service 部署到 Kubernetes
- **K8s 资源定义**: 在 `services/1_java_backend/task-service/k8s/` 目录下创建了 `deployment.yaml`，包含两个核心 K8s 资源：
  - **Deployment**: 用于定义 task-service 的期望状态，包括副本数量、容器镜像和端口
  - **Service**: 定义了一个 NodePort 类型的服务，暴露服务到外部访问

#### [Fix] 解决 minikube image load 卡死/失败的顽固问题
- **问题描述**: 执行 `minikube image load athena/task-service:0.0.1` 命令时，反复出现错误或命令长时间卡住无响应
- **解决方案 (手动模拟 image load)**:
  ```bash
  docker save athena/task-service:0.0.1 -o task-service.tar
  minikube cp task-service.tar /home/<USER>/
  minikube ssh "docker load -i /home/<USER>/task-service.tar"
  ```

### 镜像加载通用命令
```bash
# 任务服务镜像加载
docker buildx build --platform linux/amd64 -t athena/task-service:0.0.13 . --load
docker save athena/task-service:0.0.13 -o task-service-0.0.13.tar
minikube cp task-service-0.0.13.tar /home/<USER>/
minikube ssh "docker load -i /home/<USER>/task-service-0.0.13.tar"

# PINN引擎镜像加载 
docker buildx build --platform linux/amd64 -t athena/pinn-engine:0.0.16 . --load
docker save athena/pinn-engine:0.0.16 -o pinn-engine-0.0.16.tar
minikube cp pinn-engine-0.0.16.tar /home/<USER>/
minikube ssh "docker load -i /home/<USER>/pinn-engine-0.0.16.tar"

# agent引擎镜像加载 
docker buildx build --platform linux/amd64 -t athena/agent-engine:0.0.13 . --load
docker save athena/agent-engine:0.0.13 -o agent-engine-0.0.13.tar
minikube cp agent-engine-0.0.13.tar /home/<USER>/
minikube ssh "docker load -i /home/<USER>/agent-engine-0.0.13.tar"

# knowledge-service知识图谱镜像加载
docker save athena/knowledge-service:0.0.8 -o knowledge-service-0.0.8.tar
minikube cp knowledge-service-0.0.8.tar /home/<USER>/
minikube ssh "docker load -i /home/<USER>/knowledge-service-0.0.8.tar"

# api-gateway网关镜像加载
docker save athena/api-gateway:0.0.2 -o api-gateway-0.0.2.tar
minikube cp api-gateway-0.0.2.tar /home/<USER>/
minikube ssh "docker load -i /home/<USER>/api-gateway-0.0.2.tar"

# user-service用户服务镜像加载
docker save athena/user-service:0.0.2 -o user-service-0.0.2.tar
minikube cp user-service-0.0.2.tar /home/<USER>/
minikube ssh "docker load -i /home/<USER>/user-service-0.0.2.tar"

# llm-service大模型服务镜像加载
docker buildx build --platform linux/amd64 -t athena/llm-service:0.0.18 . --load
docker save athena/llm-service:0.0.18 -o llm-service-0.0.18.tar
minikube cp llm-service-0.0.18.tar /home/<USER>/
minikube ssh "docker load -i /home/<USER>/llm-service-0.0.18.tar"

# agent-executor-service镜像加载
docker buildx build --platform linux/amd64 -t athena/agent-executor-service:0.0.3 . --load
docker save athena/agent-executor-service:0.0.3 -o agent-executor-service-0.0.3.tar
minikube cp agent-executor-service-0.0.3.tar /home/<USER>/
minikube ssh "docker load -i /home/<USER>/agent-executor-service-0.0.3.tar"

```

### 阶段成果
- **打通端到端云原生部署链路**: 首次实现了从 Java 源代码到可运行的 Docker 容器，再到部署在 Kubernetes 集群中并通过网络访问的完整流程
- **掌握了 K8s 核心概念**: 通过实践，深刻理解了 Deployment、Pod、Service 等核心资源的作用和配置方法
- **积累了丰富的环境排错经验**: 成功解决了国内网络环境、Minikube 缓存等一系列复杂问题

### v0.3.0 部署完整正确流程 (复现指南)

1. **环境准备**:
   - 安装 Docker Desktop, Minikube, kubectl
   - 在 Docker Desktop 中配置好国内镜像加速器
   - 在项目根目录创建 `.gitattributes` 并配置 `* text=auto eol=lf`

2. **构建 Docker 镜像**:
   - 导航到 `athena-core/services/1_java_backend/task-service/` 目录
   - 执行构建命令：`docker buildx build --platform linux/amd64 -t athena/task-service:0.0.1 . --load`

3. **启动并配置 Minikube**:
   - 清理旧环境：`minikube delete --all`
   - 使用国内源启动：
     ```bash
     minikube start --image-repository=registry.cn-hangzhou.aliyuncs.com/google_containers --binary-mirror=https://mirror.nju.edu.cn/kubernetes/
     ```
   - (如果需要) 手动缓存 Windows kubectl.exe 到 `~/.minikube/cache/windows/amd64/<version>/`

4. **将本地镜像加载到 Minikube**:
   ```bash
   docker save athena/task-service:0.0.1 -o task-service.tar
   minikube cp task-service.tar /home/<USER>/
   minikube ssh -- docker load -i /home/<USER>/task-service.tar
   ```

5. **部署应用**:
   - 导航到 `.../task-service/k8s/` 目录
   - 执行部署：`kubectl apply -f deployment.yaml`

6. **验证与访问**:
   - 检查 Pod 状态：`kubectl get pods` (确保为 Running)
   - 暴露并访问服务：`minikube service task-service-svc`

---

## v0.4.0: 数据库集成与有状态应用部署

**发布日期**: 2025-07-05 13:03  
**核心目标**: 在 Kubernetes 集群中成功部署 PostgreSQL 数据库作为有状态应用，并改造 task-service 使其能够连接数据库、实现数据的持久化存储与读取。

### 主要变更 (Changelog)

#### [Feat] 在 Kubernetes 中部署 PostgreSQL
- **K8s 资源定义**: 在 `k8s/` 目录下创建了 `postgres.yaml`，包含以下资源：
  - **Secret**: 使用 stringData 字段安全管理数据库凭证
  - **ConfigMap**: 存储非敏感配置信息
  - **PersistentVolumeClaim (PVC)**: 声明持久化存储需求
  - **StatefulSet**: 部署 PostgreSQL 有状态应用
  - **Service (ClusterIP)**: 提供集群内部稳定访问地址

#### [Feat] task-service 集成数据库访问能力
- **技术栈引入**: 在 `pom.xml` 中引入了 Spring Boot Starter Data JPA 和 PostgreSQL JDBC Driver 依赖
- **配置中心化**: 在 `application.properties` 中关联数据源配置与环境变量
- **业务逻辑实现**:
  - 创建了 Task JPA 实体类，映射到数据库表
  - 创建了 TaskRepository 接口，简化数据库操作
  - 实现了 POST /tasks 和 GET /tasks 两个 API 端点

#### [Feat] 实现服务间依赖注入与连接
- **K8s 服务发现**: 更新了 task-service 的 deployment.yaml，通过环境变量注入实现服务发现

### 遇到的问题与解决方案

#### [Fix] PowerShell 中 curl 命令参数解析失败
- **问题描述**: PowerShell 中使用标准 curl 命令发送 POST 请求时出现各种错误
- **解决方案**: 使用 PowerShell 原生命令 Invoke-RestMethod
  ```powershell
  Invoke-RestMethod -Uri <URL> -Method Post -ContentType 'application/json' -Body <JSON_STRING>
  ```

#### [Fix] minikube service 隧道代理进程中断
- **问题描述**: API 请求报告连接失败错误，即使之前服务访问正常
- **解决方案**: 重新打开终端窗口，再次运行 `minikube service task-service-svc` 并保持窗口不关闭

#### [Fix] Docker Desktop 周期性无响应/假死
- **问题描述**: minikube 或 docker 命令周期性变得极度缓慢或完全无响应
- **解决方案**:
  - **强制重置**: 使用 `wsl --shutdown` 命令快速强制关闭卡死的 Docker 后端
  - **资源限制**: 创建并配置 `.wslconfig` 文件，限制 WSL 2 的资源使用

### 阶段成果
- **打通有状态应用部署**: 成功地在 K8s 中部署和管理了 PostgreSQL 数据库
- **实现服务依赖与发现**: 跑通了应用服务依赖并连接基础设施服务的完整链路
- **根治本地开发环境顽疾**: 解决了 Docker Desktop 在 Windows 上的不稳定问题

---

## v0.5.0: 异步通信与跨语言服务集成

**发布日期**: 2025-07-05 22:23  
**核心目标**: 引入 RabbitMQ 消息队列，将系统架构从同步调用升级为异步消息驱动模式。改造 task-service 为消息生产者，并创建第一个 Python 计算引擎 pinn-engine 作为消息消费者。

### 主要变更 (Changelog)

#### [Feat] 在 Kubernetes 中部署 RabbitMQ 消息总线
- **K8s 资源定义**: 在 `k8s/` 目录下创建了 `rabbitmq.yaml`
- **镜像选择**: 使用了 `rabbitmq:3.12-management-alpine` 镜像，同时提供服务和管理后台
- **服务暴露**: 创建了 ClusterIP 类型的 rabbitmq-svc 服务，暴露 AMQP 端口 (5672) 和管理端口 (15672)

#### [Feat] task-service (Java) 改造为消息生产者
- **技术栈引入**: 添加 spring-boot-starter-amqp 依赖
- **配置更新**: 通过 Kubernetes DNS 实现服务发现
- **消息格式统一**: 配置 Jackson2JsonMessageConverter 转换为 JSON 格式
- **业务逻辑重构**: TaskController 的 createTask 方法发送任务消息到队列

#### [Feat] 创建 pinn-engine (Python) 作为消息消费者
- **新服务骨架**: 在 `services/2_python_engines/` 目录下创建 pinn-engine 服务
- **健壮的消费者逻辑**:
  - 实现完整的消费者逻辑，连接 RabbitMQ 并消费指定队列
  - 优雅关闭: 捕获信号确保 Pod 能够正常终止
  - 连接重试: 自动重试连接机制
  - JSON 解析: 正确解析 Java 服务发送的 JSON 消息

#### [Chore] 统一队列声明参数
- 在 Java 和 Python 服务中统一将队列声明为持久化 (durable=True)

### 遇到的问题与解决方案

#### [Fix] Java 对象序列化导致 Python 无法解码
- **问题描述**: Python 在解码消息时抛出 UTF-8 编码错误
- **解决方案**: 使用 JSON 作为跨语言通信的统一数据格式

#### [Fix] RabbitMQ 队列参数冲突
- **问题描述**: 队列声明参数不一致导致连接失败
- **解决方案**: 统一队列参数并彻底清理旧队列

#### [Fix] Python 消费者 Pod 无法优雅关闭
- **问题描述**: Pod 更新时卡在 Terminating 状态
- **解决方案**: 使用 signal 模块捕获终止信号，实现非阻塞的消息处理循环

#### [Fix] pinn-engine Pod 日志无输出的"静默失败"
- **问题描述**: Pod 状态正常但无日志输出
- **解决方案**: 使用全新镜像版本绕过缓存问题

### 阶段成果
- **架构升级**: 成功将系统升级为异步消息驱动架构
- **跨语言集成**: 验证了 Java 和 Python 服务间的可靠通信
- **云原生实践深化**: 掌握了消息队列部署、服务间异步通信等技能
- **排错能力提升**: 解决了复杂的多服务交互问题

---

## v0.6.0: 实验跟踪与数据资产管理

**发布日期**: 2025-07-07 01:12  
**核心目标**: 引入 MLflow Tracking Server 和 MinIO 对象存储，实现对模拟推演的实验跟踪、参数记录、指标监控及结果文件的存储管理。

### 主要变更 (Changelog)

#### [Feat] 在 Kubernetes 中部署 MinIO (对象存储)
- **K8s 资源定义**: 在 `k8s/` 目录下创建了 `minio.yaml`
- **安全凭证管理**: 通过 Secret 安全存储访问凭证
- **服务持久化**: 配置 PVC 确保数据持久化
- **服务暴露**: 暴露 API 和管理界面端口

#### [Feat] 在 Kubernetes 中部署 MLflow Tracking Server
- **K8s 资源定义**: 在 `k8s/` 目录下创建了 `mlflow.yaml`
- **数据库后端**: 配置使用 PostgreSQL 存储元数据
- **Artifact 存储**: 配置 MinIO 作为大型结果文件存储
- **服务暴露**: 暴露 MLflow Web UI

#### [Feat] task-service (Java) 集成 MLflow 实验跟踪
- **技术栈引入**: 添加 mlflow-client 依赖
- **数据模型扩展**: 添加 mlflowRunId 关联字段
- **业务逻辑重构**: 创建 MLflow Run 并更新关联信息

#### [Feat] pinn-engine (Python) 集成 MLflow 记录实验
- **技术栈引入**: 添加 mlflow 和 minio 依赖
- **环境配置**: 注入所需的服务地址和认证信息
- **核心逻辑增强**: 记录参数、指标并上传结果文件

### 遇到的问题与解决方案

#### [Fix] MLflow 部署失败：Secret 键名不匹配
- **问题描述**: Pod 创建容器配置错误，找不到指定的 Secret 键
- **解决方案**: 修正 Secret 配置，确保包含所有必需的键

### 阶段成果
- **可复现性核心能力构建**: 实现了对科学推演全生命周期的跟踪和管理
- **数据资产化**: 引入对象存储解决了大型结果文件的存储和检索问题
- **核心链路闭环**: 打通了"Java 调度 -> Python 计算 -> MLflow 跟踪 -> MinIO 存储"链路
- **云原生排错经验**: 积累了针对 Windows 环境的深层次排错经验，增强了应对复杂环境的能力

---

## v0.7.0: 模拟引擎深化与异步数据闭环

**发布日期**: 2025-07-07 11:53 
**核心目标**: 深化 `pinn-engine` 的功能，使其从简单的消息消费者升级为能够执行模拟计算并返回结果的核心引擎。同时改造 `task-service` 以接收并处理这些结果，从而实现从“任务分发”到“结果回收”的完整异步数据闭环。

### 主要变更 (Changelog)

#### [Feat] 定义统一的模拟任务与结果数据模型
- **数据模型扩展**: 在 `task-service` 的 `Task` 实体类 (Java) 中增加了 `rainfall` (输入), `temperature` (输入), `waterLevel` (输出) 等字段，用于承载具体的模拟任务参数和结果。
- **消息契约升级**: 调整了 `task-service` 发送到 RabbitMQ 的消息格式，在原有 `mlflowRunId` 的基础上，增加了 `taskId` 和所有模拟输入参数，确保 `pinn-engine` 能获取完整的计算上下文。

#### [Feat] `task-service` (Java) 实现结果的异步接收与处理
- **结果队列定义**: 在 `RabbitMQConfig.java` 中新增了 `simulation_results` 队列，专门用于接收来自计算引擎的结果反馈。
- **结果消费者实现**: 创建了 `SimulationResultConsumer.java`，它使用 `@RabbitListener` 监听 `simulation_results` 队列。在收到结果消息后，它能根据 `taskId` 查询数据库，更新任务的 `waterLevel` 等结果字段，并最终持久化。

#### [Feat] `pinn-engine` (Python) 实现模拟计算与结果反馈
- **模拟逻辑骨架**: 在 `consumer.py` 中，用一个简单的数学函数 `simulate_water_level()` 替代了复杂的 PINN 模型，用于根据输入（降雨量、温度）模拟计算输出（水位），为后续替换成真实模型奠定了基础。
- **结果反馈链路**: `pinn-engine` 在完成模拟计算和 MLflow 记录后，会将包含 `taskId` 和 `waterLevel` 的结果消息发布到 `simulation_results` 队列，主动将计算成果反馈给上游服务。
- **MLflow 记录深化**: `pinn-engine` 现在能将具体的模拟输入参数（`rainfall`, `temperature`）记录到 MLflow 的 Params，将模拟输出（`waterLevel`）记录到 Metrics，提供了更丰富的实验追溯信息。

### 遇到的问题与解决方案

#### 无

### 阶段成果
- **完整数据闭环**: 成功构建了从“请求 -> 任务分发 -> 异步计算 -> 结果回传 -> 数据持久化”的全链路数据闭环。
- **功能性模拟**: `pinn-engine` 不再只是一个消息管道，而是具备了接收参数、执行模拟、产出结果的核心功能骨架。
- **双向异步通信**: 验证了通过 RabbitMQ 的两个不同队列，可以实现服务间的双向、异步、解耦的通信模式。
- **端到端调试能力**: 掌握了在复杂的分布式系统中，通过分层验证（网络连通性 -> 服务可用性 -> 业务逻辑正确性）和日志分析来定位问题的综合调试能力。

---

## v0.8.0: 引入预训练 PINN 模型，实现 AI 推理服务

**发布日期**: 2025-07-07 21:40
**核心目标**: 将 `pinn-engine` 从一个基于简单公式的模拟器，升级为一个真正的 AI 推理服务。通过实现“线下训练、线上推理”的 MLOps 流程，并利用 MLflow 进行模型版本管理，为平台注入了核心的“智能计算”能力。

### 主要变更 (Changelog)

#### [Feat] 线下训练与模型版本化 (Offline Training & Model Versioning)
- **PINN 模型定义**: 在 `offline_training` 目录中，使用 PyTorch 创建了一个针对水文模拟的 `HydroPINN` 模型。该模型不仅学习模拟数据，还通过自定义的物理损失函数（基于 `d(water_level)/d(temperature)` 的约束）融入了简化的物理知识。
- **模型训练与保存**: 编写了 `train_pinn.py` 脚本，在线下完成了对 `HydroPINN` 模型的训练，并将训练好的模型权重保存为 `hydro_pinn_v1.pth` 文件。
- **MLflow 模型仓库集成**: 创建了 `log_model.py` 脚本，它能加载训练好的权重，并通过 `mlflow.pytorch.log_model` 将模型上传并注册到 MLflow Tracking Server 的**模型仓库 (Model Registry)** 中，实现了模型的集中管理和版本化 (例如，`hydro-pinn-model:v1`)。

#### [Feat] `pinn-engine` (Python) 升级为 AI 推理引擎
- **技术栈升级**: 在 `pinn-engine` 的 `requirements.txt` 中引入了 `torch` 库，使其具备了运行 PyTorch 模型的能力。
- **动态模型加载**: 实现了 `model_loader.py`，它能够在服务启动时，通过 MLflow 的 API (`mlflow.pytorch.load_model`) 从模型仓库中拉取并加载指定名称和版本的预训练 PINN 模型。这使得线上推理服务与模型开发完全解耦。
- **推理逻辑替换**: `pinn-engine` 的核心计算逻辑被重构。它不再使用硬编码的数学公式，而是将接收到的输入参数（`rainfall`, `temperature`）转换为 PyTorch Tensor，并调用加载好的 `HydroPINN` 模型进行前向传播（推理），从而得到更科学、更智能的预测结果。

### 遇到的问题与解决方案

#### [Fix] PINN引擎模型加载失败
- **问题描述**: PINN引擎容器无法加载模型，MLflow中找不到模型，本地备份路径也不存在
- **解决方案**: 
  1. 在Dockerfile中添加`COPY hydro_pinn_v1.pth /app/hydro_pinn_v1.pth`将模型文件复制到容器中
  2. 确保模型文件已放置在与Dockerfile同级目录下
  3. 重新构建镜像并部署Pod

#### [Fix] RabbitMQ连接问题
- **问题描述**: PINN引擎连接RabbitMQ服务失败，出现Connection refused错误
- **解决方案**: 
  1. 确认RabbitMQ服务已正常运行（日志显示最终连接成功）
  2. 系统自动重试机制有效，无需额外干预

### 阶段成果
- **MLOps 核心流程打通**: 成功实现了“线下训练 -> 模型版本化管理 -> 线上服务加载 -> AI 推理”这一现代机器学习应用部署的核心 MLOps 流程。
- **“智能”的首次实现**: 平台的核心计算引擎首次由 AI 模型驱动，不再是简单的规则模拟，这是从“自动化”到“智能化”的关键一步。
- **模型与服务解耦**: 通过 MLflow 模型仓库，实现了算法模型开发与线上服务部署的完全分离，使得算法工程师和后端工程师可以并行工作，独立迭代。
- **可信赖性增强**: 所有由 `pinn-engine` 产出的结果，现在都可以追溯到其所使用的具体模型版本，极大地增强了平台输出结果的科学严谨性和可复现性。

---

## v0.9.0: 引入多智能体，实现社会-自然系统耦合

**发布日期**: 2025-07-10 18:22
**核心目标**: 引入全新的 `agent-engine` 服务，首次实现“社会系统”（居民用水）对“自然系统”（PINN 水文模型）的直接影响。通过构建一个由 `task-service` -> `agent-engine` -> `pinn-engine` -> `task-service` 组成的完整、多服务协作的异步消息闭环，成功验证了平台的核心理念——社会-自然耦合系统的模拟。

### 主要变更 (Changelog)

#### [Feat] 新增 `agent-engine` (Python) - 社会系统模拟核心
- **新服务骨架**: 在 Monorepo 中创建了 `agent-engine` 服务，它作为 RabbitMQ 的消费者，负责模拟社会经济主体的行为。
- **智能体逻辑定义**: 实现了简单的 `ResidentAgent` 逻辑，能够根据输入的“人口数量 (population)”和“一天中的时间 (time_of_day)”等社会因素，计算出对环境产生影响的“净取水量 (net_water_withdrawal)”。
- **作为消息中继**: `agent-engine` 在工作流中扮演了关键的“承上启下”角色。它消费来自 `task-service` 的宏观任务，将其转化为微观的智能体行为，并将这些行为作为新的参数发送给 `pinn-engine`。

#### [Feat] 构建多服务异步协作工作流
- **消息总线扩展**: 在 RabbitMQ 中新增了 `agent_actions` 和 `environment_updates` 两个队列，用于 `agent-engine` 和 `pinn-engine` 之间的双向通信，构建了更复杂的事件驱动架构。
- **`task-service` (Java) 升级**:
    - **输入参数增强**: `createTask` API 现已支持 `population` 等社会系统参数的输入。
    - **任务分发目标变更**: `task-service` 现在将初始任务消息发送到 `simulation_tasks` 队列，由 `agent-engine` 作为第一个消费者，启动整个模拟链。
- **`pinn-engine` (Python) 角色演进**:
    - **消费源变更**: `pinn-engine` 不再直接消费初始任务，而是消费来自 `agent_actions` 队列的消息，接收 `agent-engine` 计算出的社会行为参数。
    - **耦合模型推理**: PINN 水文模型在进行推理时，除了考虑自然因素（降雨量、温度），还首次将社会因素（净取水量）作为输入，实现了模型的耦合。

### 遇到的问题与解决方案

#### [Fix] 消息流中断，`pinn-engine` 无法接收到消息
- **问题描述**: 在 `agent-engine` 的日志中看到消息已发送，但 `pinn-engine` 的日志没有任何反应，整个模拟流程在中间断裂。
- **诊断过程**: 1. 首先检查 RabbitMQ 管理后台，确认 `agent_actions` 队列已创建，并且消息确实进入了队列但无人消费。 2. 检查 `pinn-engine` 的消费者代码，发现它仍在监听旧的 `simulation_tasks` 队列，而没有更新为监听新的 `agent_actions` 队列。
- **解决方案**: 修改 `pinn-engine/consumer.py`，将 `channel.queue_declare` 和 `channel.basic_consume` 的 `queue` 参数从 `'simulation_tasks'` 修改为 `'agent_actions'`，使其正确地订阅 `agent-engine` 的输出。

#### [Fix] 数据库中 `waterLevel` 字段更新延迟或不更新
- **问题描述**: 模拟流程看似已在日志中跑完，但通过 API 查询数据库时，发现对应任务的 `waterLevel` 字段长时间为 `null`。
- **诊断过程**: 1. 确认 `pinn-engine` 已将最终结果发送到 `simulation_results` 队列。 2. 检查 `task-service` 的日志，发现 `SimulationResultConsumer` 成功接收并处理了消息。 3. 最终定位到问题是由于在 `SimulationResultConsumer` 中，更新了 `Task` 对象后，忘记调用 `taskRepository.save(task)` 方法来将变更持久化到数据库。
- **解决方案**: 在 `task-service` 的 `SimulationResultConsumer.java` 的消息处理方法中，在 `task.setWaterLevel(...)` 之后，明确地调用 `taskRepository.save(task)`，确保数据变更被写入数据库。

### 本次迭代的价值
- **核心愿景的首次实现**: 成功搭建了一个包含社会和自然因素相互作用的模拟闭环，这是对项目“社会-自然耦合系统”核心愿景的第一次、也是最重要的一次概念验证。
- **架构复杂度的提升与验证**: 验证了平台在多个 Python 和 Java 服务之间，通过多条消息队列进行复杂、有序的异步通信的能力，证明了当前架构的可扩展性。
- **面向场景的开发**: 本次迭代不再是纯粹的技术模块搭建，而是围绕一个具体的、有业务意义的场景（居民用水影响水位）进行开发，使得平台的功能更加贴近实际应用。

---

## v0.10.0: 动态反馈循环与环境适应性智能体

**发布日期**: 2025-07-13 21:32
**核心目标**: 将系统从单向影响（Agent -> PINN）升级为真正的双向、多轮交互。通过实现"感知-决策-行动-反馈"的完整闭环，使智能体能够根据环境变化动态调整其行为，从而展示出适应性和学习能力。

### 主要变更 (Changelog)

#### [Feat] 实现多轮决策循环架构
- **循环控制机制**: 在 `agent-engine` 中实现了决策循环控制逻辑，支持最多5轮连续的环境-智能体交互。
- **环境状态传递**: 扩展了 `environment_updates` 队列的功能，使其能够将 PINN 引擎计算的最新水位数据反馈给智能体。
- **MLflow 参数前缀**: 为避免多轮循环中的参数冲突，在 MLflow 记录中为每轮循环的参数和指标添加了循环序号前缀。

#### [Feat] 增强 ResidentAgent 的环境感知与适应能力
- **环境状态跟踪**: 在 `ResidentAgent` 类中添加了 `current_water_level` 属性和阈值参数，使智能体能够跟踪环境状态的变化。
- **动态适应机制**: 实现了 `update_environment_state` 方法，根据水位变化动态调整 `adaptation_factor`：
  - 水位正常（>4.2米）: 逐步恢复正常用水量
  - 水位低于预警阈值（<4.2米）: 实施中度节水措施
  - 水位低于严重预警阈值（<3.5米）: 实施严格节水措施
- **节水政策集成**: 通过 `knowledge-service` 获取并应用节水政策因子，实现了"政策-智能体-环境"的三重耦合。

#### [Feat] 知识服务模型优化与数据存储
- **模型重构**: 将 `Policy` 模型中的嵌套 `attributes` Map 改为直接的 `waterSavingFactor` 属性，解决了Neo4j不支持嵌套Map的问题。
- **数据初始化**: 在 `DataInitializer` 中设置了默认的节水因子（0.8），为智能体提供了基础决策参数。

#### [Feat] PINN引擎环境模拟增强
- **参数调整**: 采用更真实的水库参数（深度5米、流域面积5平方公里），使模拟更贴近实际。
- **多因素模拟**: 在水位计算中考虑了蒸发、渗透和降雨的综合影响，增强了模型的物理合理性。
- **多轮交互支持**: 增加了对同一任务ID多次交互的支持，使PINN引擎能够在循环中持续提供环境更新。

### 遇到的问题与解决方案

#### [Fix] MLflow运行ID冲突
- **问题描述**: 多轮循环中，重复使用相同的MLflow运行ID导致"Run already active"错误。
- **解决方案**: 在PINN引擎中添加了循环前缀，并使用嵌套运行（nested=True）解决了ID冲突问题。

#### [Fix] Neo4j属性存储错误
- **问题描述**: Neo4j不支持在节点属性中使用嵌套Map结构，导致数据初始化失败。
- **解决方案**: 将Policy模型中的attributes Map替换为直接的waterSavingFactor属性，并相应修改了API和客户端代码。

#### [Fix] 节水因子未正确应用
- **问题描述**: 尽管知识服务中设置了节水因子0.8，但智能体仍使用默认值1.0。
- **解决方案**: 修改了agent-engine中的get_water_saving_factor方法，使其能够正确解析API返回的waterSavingFactor字段。

#### [Fix] Neo4j Pod 持续崩溃，状态陷入 `CrashLoopBackOff`

**问题描述**: 在 Kubernetes 中部署 Neo4j 后，其 Pod 在启动后几秒钟内就迅速进入 `Error` 状态，并被 Kubernetes 反复重启，最终陷入 `CrashLoopBackOff` 循环，导致数据库服务完全不可用。

**诊断过程**:
    1.  **初步排查**: 首先怀疑是文件系统权限问题，尝试在 Deployment 的 `securityContext` 中添加 `fsGroup: 7474`，但问题依旧存在。
    2.  **日志分析 (关键步骤)**: 使用 `kubectl logs <pod-name> --previous` 命令获取了容器上一次崩溃前的日志。日志中明确指出了错误原因：
        ```
        Failed to read config: Unrecognized setting. No declared setting with name: SVC.SERVICE.PORT.
        ```
        这表明 Neo4j 在启动时，因为它不认识某个由 Kubernetes 自动注入的环境变量（名称类似 `..._SVC_SERVICE_PORT`），并且其配置校验模式非常严格，所以直接启动失败。这是一个典型的“环境变量污染”导致应用启动失败的问题。

**解决方案**:
    1.  **定位解决方案**: 根据 Neo4j 日志的提示 "disable 'server.config.strict_validation.enabled' to continue"，确定了需要禁用 Neo4j 的严格配置校验功能。
    2.  **修改 Kubernetes Deployment**: 打开 `neo4j.yaml` 文件，在 Neo4j 容器的环境变量（`env`）部分，添加一个新的环境变量来覆盖 Neo4j 的默认配置。
        ```yaml
        - name: NEO4J_server_config_strict__validation_enabled
          value: "false"
        ```
        这个环境变量遵循了 Neo4j 的命名规则（`NEO4J_` 前缀，用 `__` 替换 `.`），其作用是告诉 Neo4j 进程在启动时，如果遇到不认识的配置项，请忽略而不是报错退出。
    3.  **重新部署**: 执行 `kubectl apply -f neo4j.yaml` 应用修改。新的 Pod 成功启动并稳定地保持在 `Running` 状态，问题解决。


#### [Fix] Neo4j Browser 登录失败，报告 WebSocket 连接错误

**问题描述**: 使用 `minikube service neo4j-svc --url` 获取访问地址后，虽然可以在浏览器中成功打开 Neo4j Browser 的登录页面，但在点击“Connect”按钮时，连接失败，并提示 `ServiceUnavailable: WebSocket connection failure`。

**诊断过程**:
    1.  **初步分析**: 错误信息明确指出是 WebSocket 连接失败。这表明浏览器前端页面（通过 HTTP 加载）与 Neo4j 数据库后端（通过 Bolt/WebSocket 协议通信）之间的连接建立失败。
    2.  **定位原因**: 意识到 `minikube service` 命令创建的是一个 HTTP 代理/隧道。这种代理在处理从 HTTP 到 WebSocket 的协议升级时可能存在兼容性问题，或者浏览器的 CORS（跨源资源共享）安全策略阻止了从 `127.0.0.1:<portA>` 的页面向 Minikube 内部 IP 地址 ` <pod-ip>:<portB>` 发起的 WebSocket 连接。

**解决方案**:
    1.  **更换连接工具**: 放弃使用 `minikube service`，改用一个更底层、更可靠的端口转发工具 `kubectl port-forward`。这个工具建立的是纯粹的 TCP 隧道，对上层协议透明，能完美支持 WebSocket。
    2.  **建立端口转发**: 停止 `minikube service` 进程，并执行以下命令，将 Neo4j Pod 的 HTTP (7474) 和 Bolt (7687) 端口直接转发到本地机器的同名端口：
        ```bash
        kubectl port-forward <neo4j-pod-full-name> 7474:7474 7687:7687
        ```
    3.  **更新访问方式**: 在浏览器中访问 `http://localhost:7474`。
    4.  **修正连接配置 (关键步骤)**: 在 Neo4j Browser 的登录界面，手动将“Connect URL”修改为 `bolt://localhost:7687`，以确保 WebSocket 连接也指向本地转发的端口。
    5.  **重新登录**: 使用正确的用户名和密码成功登录，问题解决。

### 阶段成果
- **完整闭环实现**: 成功构建了"感知-决策-行动-反馈"的完整闭环，使系统具备了真正的动态适应能力。
- **多轮交互验证**: 通过5轮连续的智能体-环境交互，验证了系统在持续变化中的稳定性和一致性。
- **知识-行为-环境耦合**: 实现了从知识库获取的政策到智能体行为，再到环境变化的完整链路，展示了系统的多层次耦合能力。
- **适应性决策机制**: 智能体能够根据环境状态动态调整其行为，展示了简单但有效的适应性学习能力。

这一里程碑标志着平台从"单向模拟"向"动态交互系统"的重要跨越，为后续更复杂的社会-自然耦合模拟奠定了坚实基础。

---

## v0.11.0: 构建 API 网关与统一认证授权

**发布日期**: 2025-07-14 0:07
**核心目标**: 引入 API 网关作为系统入口，并实现基于 JWT 的统一认证授权机制，保障系统安全性的同时实现服务间的松耦合与透明路由。

### 主要变更 (Changelog)

#### [Feat] 构建 API Gateway 服务
- **网关搭建**: 在 `services/0_gateway/apigateway/` 路径下创建了基于 Spring Cloud Gateway 的 API 网关服务
- **路由规则定义**: 在 `application.yaml` 中配置了基于路径的路由规则，将 `/api/tasks/**` 请求转发到 `task-service-svc` 服务，`/auth/**` 请求转发到 `user-service-svc` 服务
- **Kubernetes服务发现**: 启用了基于 Kubernetes DNS 的服务发现机制，实现了服务间的透明通信

#### [Feat] 实现 JWT 认证授权体系
- **JWT 工具类开发**: 在 `user-service` 中实现了 `JwtUtil` 工具类，支持 JWT 令牌的生成、验证和解析
- **认证接口实现**: 在 `user-service` 中创建了 `AuthController`，提供 `/auth/login` 端点用于用户认证和令牌颁发
- **全局过滤器实现**: 在 API 网关中实现了 `AuthenticationFilter` 全局过滤器，对除登录接口外的所有请求进行 Token 验证
- **跨服务共享**: 在 API 网关和用户服务间共享了相同的 JWT 密钥，确保令牌在整个系统中的一致性

#### [Feat] 微服务容器化与 Kubernetes 部署
- **多阶段构建优化**: 为 `api-gateway` 和 `user-service` 创建了基于 `maven:3.9.6-eclipse-temurin-17` 和 `eclipse-temurin:17-jre-alpine` 的多阶段 Dockerfile
- **Kubernetes 资源定义**: 为两个服务分别创建了 `deployment.yaml` 和 `service.yaml`，其中 `api-gateway-svc` 配置为 `NodePort` 类型，暴露给外部访问，`user-service-svc` 则为内部 `ClusterIP` 类型

### 遇到的问题与解决方案

#### [Fix] Spring Cloud Gateway 依赖不匹配
- **问题描述**: API 网关构建失败，日志中报错 `package org.springframework.cloud.gateway.filter does not exist`
- **诊断过程**: 分析 `pom.xml` 发现使用了不兼容的 `spring-cloud-starter-gateway-server-webmvc` 依赖，而代码中使用的是响应式模型
- **解决方案**: 将 `pom.xml` 中的依赖替换为正确的 `spring-cloud-starter-gateway`，解决了响应式编程模型的依赖冲突

#### [Fix] 网关过滤器令牌解析失败
- **问题描述**: 使用 curl 访问受保护的资源时，API 网关始终返回 401 Unauthorized，即使使用了有效的 Token
- **诊断过程**: 通过日志分析，发现过滤器在尝试解析格式为 `Authorization: Bearer <token>` 的请求头时出错
- **解决方案**: 修正了 `AuthenticationFilter` 中的令牌提取逻辑，确保正确处理 Bearer 前缀和空格

#### [Fix] Windows PowerShell 中的 HTTP 请求处理
- **问题描述**: 在验证 API 时，PowerShell 中的 `Invoke-RestMethod` 命令不支持 `-SkipHttpErrorCheck` 参数
- **解决方案**: 使用 `try...catch` 块替代，捕获 HTTP 错误并检查响应状态码，保持了测试流程的一致性

### 阶段成果
- **统一入口点**: 成功构建了系统的统一入口点，所有外部请求现在都通过 API 网关进行路由和过滤
- **认证安全体系**: 实现了基于 JWT 的完整认证授权流程，保障了系统的访问安全
- **微服务松耦合**: 通过网关和服务发现机制，使得后端服务可以独立演进，同时维持统一的接口和安全策略
- **Kubernetes原生部署**: 所有组件均采用 Kubernetes 原生方式部署，具备了容器化和云原生应用的特性

本次迭代使平台从一个功能性原型升级为具备基本生产能力的系统，为后续扩展更多业务功能和更复杂的社会-自然耦合模型奠定了坚实的基础架构。

## v0.12.0: 大模型流式响应与容错能力增强

**发布日期**: 2025-07-15 0:10
**核心目标**: 升级LLM服务，实现流式响应功能并增强系统容错能力，确保在各种异常情况下仍能提供稳定的服务。

### 主要变更 (Changelog)

#### [Feat] 实现LLM服务流式响应API
- **异步处理优化**: 将`/explain-stream`端点从同步函数改造为异步函数，以支持FastAPI的异步流式响应机制
- **流处理改进**: 修复了OpenAI流对象在异步环境中的处理方式，通过添加`asyncio.sleep()`确保异步协程正常执行
- **客户端体验提升**: 用户现在可以看到实时生成的文本，而不是等待整个响应完成，大幅提升了交互体验

#### [Feat] 增强系统容错与降级能力
- **模拟数据支持**: 为数据库、MLflow和知识服务查询添加了模拟数据回退机制，确保在外部依赖不可用时服务仍能继续运行
- **异常处理强化**: 在关键环节添加异常捕获并提供清晰的错误信息，便于快速诊断和解决问题
- **日志完善**: 增强了关键操作的日志记录，提供详细的错误上下文信息

#### [Chore] 容器镜像与部署更新
- **镜像版本升级**: 将LLM服务镜像从`0.0.1`版本升级到`0.0.4`版本
- **部署配置优化**: 更新Kubernetes部署配置，确保新版本服务正确部署和运行

### 遇到的问题与解决方案

#### [Fix] OpenAI流对象无法在异步环境使用
- **问题描述**: 流式响应API返回错误`object Stream can't be used in 'await' expression`
- **诊断过程**: 分析发现OpenAI客户端的流对象不支持异步迭代协议(`__aiter__`方法)
- **解决方案**: 将`async for chunk in completion`改回使用同步的`for chunk in completion`，但在迭代中添加`await asyncio.sleep(0.01)`，让出控制权给其他协程

#### [Fix] 外部服务连接失败导致LLM服务中断
- **问题描述**: 数据库和知识服务连接失败，导致整个API调用链路中断
- **诊断过程**: 日志显示PostgreSQL认证失败和知识服务DNS解析失败
- **解决方案**: 为所有外部依赖调用添加try-except处理，在捕获异常后返回合理的模拟数据，确保服务降级而不是完全失败

### 阶段成果
- **流式交互体验**: 成功实现了LLM服务的流式响应，使用户能够实时看到生成的内容，显著提升了交互体验
- **系统韧性增强**: 通过完善的错误处理和服务降级机制，系统在面对各种异常情况时表现出更强的韧性
- **开发效率提升**: 更详细的日志和错误信息，大幅缩短了问题诊断和解决的时间
- **架构设计优化**: 流式处理的实现验证了系统架构对实时性要求的支持能力，为未来更多实时交互功能奠定了基础

本次迭代使平台在用户体验和系统稳定性方面有了显著提升，特别是流式响应功能的实现，为后续基于大模型的更多交互式应用提供了技术支持。

## v0.13.0: 认知分析升级：集成RAG与修复端到端数据链路

**发布日期**: 2025-07-17 15:03
**核心目标**: 部署向量数据库ChromaDB，建立文档向量化存储管道。将RAG（检索增强生成）能力无缝融入`llm-service`，并修复所有后端服务间的数据链路，最终实现一个能够结合结构化数据与非结构化知识、进行深度因果分析并流式输出报告的认知分析核心。

### 主要变更 (Changelog)

#### [Feat] 部署ChromaDB向量数据库与文档处理管道 (v0.13.0)
- **Kubernetes资源定义**: 在`k8s/infrastructure/`目录下创建了`chromadb.yaml`文件，定义了包含持久卷声明、Deployment和Service的完整资源。
- **持久化配置**: 为ChromaDB配置了2GB的持久卷，确保Pod重启后向量数据不丢失。
- **文档处理脚本**: 创建了`ingest_docs.py`脚本，实现了从本地目录加载文本文档、使用`RecursiveCharacterTextSplitter`进行智能分块、并利用本地`all-MiniLM-L6-v2`嵌入模型生成向量的完整流程。
- **数据注入**: 实现了将文本块及其向量批量注入到ChromaDB中名为`athena_knowledge_base`的集合的逻辑。

#### [Feat] `llm-service`全面升级，融合RAG能力 (v0.13.5)
- **RAG集成**: 在`llm-service`中添加了ChromaDB客户端，实现了完整的RAG检索逻辑。现在，服务可以根据用户问题或任务上下文，从向量库中动态检索最相关的背景知识。
- **Prompt工程增强**: 重构了Prompt模板，创建了包含**“思维链 (Chain of Thought)”**的增强版Prompt。该模板能够结构化地整合来自**PostgreSQL、MLflow、Neo4j和RAG**四大数据源的证据。
- **统一分析入口**: 废弃了独立的RAG查询端点，将RAG功能无缝融入到核心的`/explain-stream`端点中，实现了对模拟任务的统一、深度的分析能力。
- **开源模型嵌入**: 在`llm-service`中同样集成了本地的`all-MiniLM-L6-v2`模型，确保了数据注入和查询时使用的嵌入空间完全一致，保证了检索的准确性。

### 遇到的问题与解决方案

#### [Fix] ChromaDB部署与兼容性系列问题
- **镜像拉取失败**: `ImagePullBackOff`问题通过将镜像地址从`chroma/chroma:latest`修正为具体的稳定版本`chromadb/chroma:0.4.24`解决。
- **Python依赖冲突**: Python 3.8缺少`graphlib`模块的问题，通过安装`graphlib-backport`兼容包解决。
- **客户端与服务器版本不匹配**: `NotImplementedError`问题，通过统一客户端与服务器的ChromaDB版本为`0.4.24`解决。
- **数据库架构不匹配**: `no such column: collections.topic`问题，通过在K8s Deployment中设置`ALLOW_RESET=true`环境变量，并重新创建集合解决。

#### [Fix] 模型下载与容器冷启动问题
- **网络代理导致模型下载失败**: 初次运行脚本时，Hugging Face模型下载失败。通过手动下载模型到本地`models`目录，并修改脚本引用本地路径解决。
- **容器启动时重复下载**: `llm-service` Pod每次启动都重新下载嵌入模型，导致启动缓慢。通过修改`llm-service`的`Dockerfile`，增加一个`RUN python -c "..."`命令来**“烘焙”模型**，将模型文件预置到镜像中，实现了服务的秒级启动。

#### [Fix] 端到端数据链路中的“静默失败”系列问题
- **MLflow数据为空**:
    - **问题描述**: `llm-service`生成的报告显示MLflow数据为空，但`pinn-engine`日志和MLflow UI均显示数据已成功记录。
    - **诊断过程**: 认识到这是一个**时序问题**。手动调用`llm-service`的时机，早于异步的`pinn-engine`完成数据写入的时机。
    - **解决方案 (手动验证)**: 在触发模拟后，耐心等待后台异步流程完全结束，再调用分析接口，成功获取到了完整的MLflow数据。

- **`knowledge-service`连接失败**:
    - **问题描述**: `llm-service`日志显示`Failed to resolve 'knowledge-service'`。
    - **诊断过程**: `kubectl get svc`发现`knowledge-service`的实际服务名称是`knowledge-service-svc`。
    - **解决方案**: 修改`llm-service`的Deployment YAML，将注入的环境变量中的服务地址修正为`http://knowledge-service-svc:80`。

- **`knowledge-service`返回空结果**:
    - **问题描述**: 连接成功但API返回空，导致`agent-engine`使用默认因子。
    - **诊断过程**: 在Neo4j Browser中直接执行`knowledge-service`的Cypher查询，发现**关系方向反了**。
    - **解决方案**: 修正了`knowledge-service`代码中的Cypher查询语句，使其与数据库中` (City)-[:IMPLEMENTS]->(Policy)`的数据结构完全匹配。

### 阶段成果
- **混合知识库建成**: 平台现在同时拥有了用于存储**结构化事实的知识图谱 (Neo4j)**和用于存储**非结构化上下文的向量知识库 (ChromaDB)**。
- **认知分析核心成型**: `llm-service`成功演进为一个能够**融合多源异构数据**，进行**可解释的、基于思维链的因果分析**，并**流式输出**高质量自然语言报告的强大认知引擎。
- **端到端数据链路修复**: 彻底解决了从`task-service`到`llm-service`所有服务间的数据可见性和时序问题，确保了信息流的完整性和可靠性。
- **系统健壮性提升**: 通过解决一系列复杂的部署、兼容性和逻辑问题，整个平台的稳定性和可维护性得到了极大的提升。

本次迭代标志着“雅典娜”平台的核心认知分析能力已基本构建完成。平台不仅能“做”模拟，更能“看懂”和“说清”模拟结果背后的深层逻辑，为实现真正的决策支持奠定了坚实的基础。

