# Athena Core 部署指南

## v1.0.0: Kubernetes集群完整部署流程

**发布日期**: 2023-08-20  
**核心目标**: 在Kubernetes集群中按正确的依赖顺序部署所有服务组件，确保系统正常运行。

### 主要步骤 (Deployment Process)

#### [Step 1] 部署基础设施服务

基础设施是整个系统的支撑，需要首先部署这些服务，并确保其正常运行。

```bash
# 部署Secrets
kubectl apply -f services/1_java_backend/knowledge-service/k8s/neo4j-secret.yaml
kubectl apply -f services/1_java_backend/task-service/k8s/minio-secret.yaml
kubectl apply -f services/2_python_engines/llm-service/k8s/llm-secrets.yaml

# 部署数据库和消息队列
kubectl apply -f services/1_java_backend/task-service/k8s/postgres.yaml
[postgres:14-alpine]
kubectl apply -f services/1_java_backend/task-service/k8s/neo4j.yaml
[neo4j:5-community]
kubectl apply -f services/1_java_backend/task-service/k8s/rabbitmq.yaml
[rabbitmq:3.12-management-alpine]
kubectl apply -f services/1_java_backend/task-service/k8s/minio.yaml
[minio/minio:latest]
kubectl apply -f services/1_java_backend/task-service/k8s/mlflow.yaml
[ghcr.io/mlflow/mlflow:latest]
kubectl apply -f services/2_python_engines/llm-service/k8s/chromadb.yaml
[ghcr.io/chroma-core/chroma:0.4.24]

# 等待这些服务启动并就绪
kubectl wait --for=condition=ready pod -l app=postgres --timeout=180s
kubectl wait --for=condition=ready pod -l app=neo4j --timeout=180s
kubectl wait --for=condition=ready pod -l app=rabbitmq --timeout=180s
kubectl wait --for=condition=ready pod -l app=minio --timeout=180s
kubectl wait --for=condition=ready pod -l app=mlflow --timeout=180s
kubectl wait --for=condition=ready pod -l app=chromadb --timeout=180s
```

#### [Step 2] 部署Java后端服务

在基础设施服务正常运行后，接着部署Java后端服务。

```bash
# 部署知识服务
kubectl apply -f services/1_java_backend/knowledge-service/k8s/deployment.yaml

# 部署任务服务
kubectl apply -f services/1_java_backend/task-service/k8s/deployment.yaml

# 部署用户服务
kubectl apply -f services/1_java_backend/userservice/k8s/deployment.yaml
kubectl apply -f services/1_java_backend/userservice/k8s/service.yaml

# 等待这些服务启动并就绪
kubectl wait --for=condition=ready pod -l app=knowledge-service --timeout=180s
kubectl wait --for=condition=ready pod -l app=task-service --timeout=180s
kubectl wait --for=condition=ready pod -l app=user-service --timeout=180s
```

#### [Step 3] 部署Python引擎服务

Java后端服务就绪后，部署负责智能计算的Python引擎服务。

```bash
# 部署LLM服务
kubectl apply -f services/2_python_engines/llm-service/k8s/deployment.yaml

# 部署PINN引擎
kubectl apply -f services/2_python_engines/pinn-engine/k8s/deployment.yaml

# 部署Agent引擎
kubectl apply -f services/2_python_engines/agent-engine/k8s/deployment.yaml

# 等待这些服务启动并就绪
kubectl wait --for=condition=ready pod -l app=llm-service --timeout=180s
kubectl wait --for=condition=ready pod -l app=pinn-engine --timeout=180s
kubectl wait --for=condition=ready pod -l app=agent-engine --timeout=180s
```

#### [Step 4] 最后部署API网关

所有后端服务就绪后，最后部署API网关作为系统入口点。

```bash
# 部署API网关
kubectl apply -f services/0_gateway/apigateway/k8s/deployment.yaml
kubectl apply -f services/0_gateway/apigateway/k8s/service.yaml

# 等待API网关启动并就绪
kubectl wait --for=condition=ready pod -l app=api-gateway --timeout=180s
```

### 镜像准备 (Image Preparation)

在部署前，需要确保所有镜像已经加载到Kubernetes集群中。如果使用minikube，以下是镜像加载的命令：

```bash
# 任务服务镜像加载
docker save athena/task-service:0.0.10 -o task-service-0.0.10.tar
minikube cp task-service-0.0.10.tar /home/<USER>/
minikube ssh "docker load -i /home/<USER>/task-service-0.0.10.tar"

# PINN引擎镜像加载 
docker save athena/pinn-engine:0.0.14 -o pinn-engine-0.0.14.tar
minikube cp pinn-engine-0.0.14.tar /home/<USER>/
minikube ssh "docker load -i /home/<USER>/pinn-engine-0.0.14.tar"

# agent引擎镜像加载 
docker save athena/agent-engine:0.0.9 -o agent-engine-0.0.9.tar
minikube cp agent-engine-0.0.9.tar /home/<USER>/
minikube ssh "docker load -i /home/<USER>/agent-engine-0.0.9.tar"

# knowledge-service知识图谱镜像加载
docker save athena/knowledge-service:0.0.8 -o knowledge-service-0.0.8.tar
minikube cp knowledge-service-0.0.8.tar /home/<USER>/
minikube ssh "docker load -i /home/<USER>/knowledge-service-0.0.8.tar"

# api-gateway网关镜像加载
docker save athena/api-gateway:0.0.2 -o api-gateway-0.0.2.tar
minikube cp api-gateway-0.0.2.tar /home/<USER>/
minikube ssh "docker load -i /home/<USER>/api-gateway-0.0.2.tar"

# user-service用户服务镜像加载
docker save athena/user-service:0.0.2 -o user-service-0.0.2.tar
minikube cp user-service-0.0.2.tar /home/<USER>/
minikube ssh "docker load -i /home/<USER>/user-service-0.0.2.tar"

# llm-service大模型服务镜像加载
docker buildx build --platform linux/amd64 -t athena/llm-service:0.0.6 . --load
docker save athena/llm-service:0.0.6 -o llm-service-0.0.6.tar
minikube cp llm-service-0.0.6.tar /home/<USER>/
minikube ssh "docker load -i /home/<USER>/llm-service-0.0.6.tar"
```

### 验证部署 (Deployment Verification)

部署完成后，检查所有Pod是否正常运行：

```bash
kubectl get pods
```

确保所有Pod状态为Running，并且READY列显示所有容器都已就绪。

### 系统重新部署 (System Redeployment)

如需重新部署整个系统，首先清理现有资源，然后按上述步骤重新部署：

```bash
# 删除所有服务
kubectl delete -f services/0_gateway/apigateway/k8s/
kubectl delete -f services/1_java_backend/knowledge-service/k8s/
kubectl delete -f services/1_java_backend/task-service/k8s/
kubectl delete -f services/1_java_backend/userservice/k8s/
kubectl delete -f services/2_python_engines/agent-engine/k8s/
kubectl delete -f services/2_python_engines/llm-service/k8s/
kubectl delete -f services/2_python_engines/pinn-engine/k8s/

# 等待所有资源被清理
kubectl wait --for=delete pod -l app=api-gateway --timeout=60s
kubectl wait --for=delete pod -l app=knowledge-service --timeout=60s
kubectl wait --for=delete pod -l app=task-service --timeout=60s
kubectl wait --for=delete pod -l app=user-service --timeout=60s
kubectl wait --for=delete pod -l app=agent-engine --timeout=60s
kubectl wait --for=delete pod -l app=llm-service --timeout=60s
kubectl wait --for=delete pod -l app=pinn-engine --timeout=60s
kubectl wait --for=delete pod -l app=postgres --timeout=60s
kubectl wait --for=delete pod -l app=neo4j --timeout=60s
kubectl wait --for=delete pod -l app=rabbitmq --timeout=60s
kubectl wait --for=delete pod -l app=minio --timeout=60s
kubectl wait --for=delete pod -l app=mlflow --timeout=60s
kubectl wait --for=delete pod -l app=chromadb --timeout=60s
```

### 常见问题与解决方案 (Common Issues and Solutions)

#### [Fix] Neo4j Pod 启动失败（CrashLoopBackOff）

- **问题描述**: Neo4j Pod反复崩溃，无法正常启动
- **解决方案**: 在Neo4j的部署配置中添加环境变量，禁用严格配置验证
  ```yaml
  - name: NEO4J_server_config_strict__validation_enabled
    value: "false"
  ```

#### [Fix] 镜像拉取失败（ImagePullBackOff）

- **问题描述**: Pod无法启动，状态显示为ImagePullBackOff
- **解决方案**: 使用本地镜像加载方法（如上述镜像准备部分所示）手动将镜像加载到集群中

#### [Fix] 服务间通信失败

- **问题描述**: 服务能够启动，但无法互相通信，日志中显示连接错误
- **解决方案**: 确认所有服务都使用正确的服务名（格式为`<service-name>-svc`）进行通信，并检查网络策略是否正确配置

### 系统架构图 (System Architecture)

```
                  ┌─────────────┐
                  │  API Gateway │
                  └──────┬──────┘
                         │
         ┌───────────────┼────────────────┐
         │               │                │
┌────────▼──────┐  ┌─────▼─────┐   ┌─────▼─────┐
│ User Service  │  │Task Service│   │Knowledge  │
└────────┬──────┘  └─────┬─────┘   │  Service  │
         │               │         └─────┬─────┘
         │               │               │
┌────────▼──────┐  ┌─────▼─────┐   ┌─────▼─────┐
│   Database    │  │ RabbitMQ  │   │   Neo4j   │
└───────────────┘  └─────┬─────┘   └───────────┘
                         │
         ┌───────────────┼────────────────┐
         │               │                │
┌────────▼──────┐  ┌─────▼─────┐   ┌─────▼─────┐
│ Agent Engine  │  │PINN Engine│   │LLM Service│
└───────────────┘  └───────────┘   └─────┬─────┘
                                         │
                                   ┌─────▼─────┐
                                   │ ChromaDB  │
                                   └───────────┘
```

### 阶段成果

- **完整部署流程**: 成功定义了整个系统的部署顺序和步骤，确保所有服务能够按照依赖关系正确启动
- **镜像加载方案**: 解决了在离线或受限环境下的镜像加载问题
- **问题排查指南**: 提供了常见部署问题的诊断和解决方法
- **系统架构可视化**: 通过架构图展示了各服务间的关系和交互

这一部署指南使团队能够快速地在新环境中部署整个系统，减少了环境差异带来的问题，提高了开发和运维效率。 